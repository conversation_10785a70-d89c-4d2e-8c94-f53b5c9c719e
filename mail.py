import re
import datetime
import calendar
from datetime import timedelta
from ftplib import FTP
from io import BytesIO
import pandas as pd
import mysql.connector
import numpy as np
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
import os

# FTP Configuration
FTP_HOST = "*************"
FTP_PORT = 21
FTP_USER = "partner"
FTP_PASS = "jEm9P6182x89"

# Remote FTP folder paths
FTP_PATH_LOAD = "/WIND/Kudligi/IV_Partner/Load/"

# Email Configuration
SMTP_SERVER = "mx1.50hertz.in"
SMTP_PORT = 587  # Changed from 25 to 587 for TLS
SMTP_USE_SSL = False  # Use TLS instead of SSL
SENDER_EMAIL = "<EMAIL>"
SENDER_PASSWORD = "$%^M@L*@CP"

# Backup SMTP settings in case primary fails
BACKUP_SMTP_SERVER = "smtp.gmail.com"
BACKUP_SMTP_PORT = 587
BACKUP_SMTP_USE_SSL = False  # Use TLS for backup server

# Last resort local SMTP settings (no authentication)
LOCAL_SMTP_SERVER = "localhost"
LOCAL_SMTP_PORT = 25
LOCAL_SMTP_USE_AUTH = False  # Don't use authentication for local server

# MySQL Connection
def get_db_connection():
    conn = mysql.connector.connect(
        host='localhost',
        port=3306,
        user='root',
        password='271220021247',
        database='FP',
        ssl_verify_cert=False,
        use_pure=True
    )
    return conn

# Create email_addresses table if it doesn't exist
def create_email_table():
    conn = get_db_connection()
    cursor = conn.cursor()

    create_email_table = """
    CREATE TABLE IF NOT EXISTS buyer_emails (
        buyer_name VARCHAR(100) PRIMARY KEY,
        email_address VARCHAR(255) NOT NULL
    );
    """
    cursor.execute(create_email_table)
    conn.commit()
    cursor.close()
    conn.close()
    print("Email table created successfully.")

# FTP Connection
def connect_ftp():
    ftp = FTP()
    ftp.connect(FTP_HOST, FTP_PORT)
    ftp.login(FTP_USER, FTP_PASS)
    return ftp

def find_file_for_date(ftp, remote_folder, target_date):
    """
    For Load: target file is yyyymmdd.xlsx; if not available then yyyymmdd.csv.
    """
    ftp.cwd(remote_folder)
    target_filename_xlsx = target_date.strftime("%Y%m%d") + ".xlsx"
    target_filename_csv = target_date.strftime("%Y%m%d") + ".csv"

    files = ftp.nlst()
    if target_filename_xlsx in files:
        return target_filename_xlsx
    elif target_filename_csv in files:
        return target_filename_csv
    return None

def download_ftp_file(ftp, remote_folder, remote_filename):
    """
    Downloads a file from the FTP server and returns a BytesIO stream.
    """
    ftp.cwd(remote_folder)
    bio = BytesIO()
    try:
        ftp.retrbinary(f"RETR {remote_filename}", bio.write)
        bio.seek(0)
        return bio
    except Exception as e:
        print(f"Error downloading {remote_filename} from {remote_folder}: {e}")
        return None

def get_load_data_for_today():
    """
    Fetches load data for today from FTP server and returns a DataFrame
    """
    today = datetime.date.today()
    ftp = connect_ftp()

    load_filename = find_file_for_date(ftp, FTP_PATH_LOAD, today)
    if not load_filename:
        print(f"No load file found for today ({today.strftime('%Y%m%d')})")
        ftp.quit()
        return None

    bio = download_ftp_file(ftp, FTP_PATH_LOAD, load_filename)
    ftp.quit()

    if not bio:
        return None

    if load_filename.lower().endswith(".xlsx"):
        df = pd.read_excel(bio)
    else:
        df = pd.read_csv(bio)

    return df

def get_buyer_emails():
    """
    Retrieves buyer email addresses from the database
    """
    conn = get_db_connection()
    query = "SELECT buyer_name, email_address FROM buyer_emails"
    cursor = conn.cursor(dictionary=True)
    cursor.execute(query)
    result = cursor.fetchall()
    cursor.close()
    conn.close()

    # Convert to dictionary for easy lookup
    email_dict = {row['buyer_name']: row['email_address'] for row in result}
    return email_dict

def format_load_data_for_email(df, buyer_name):
    """
    Formats load data for a specific buyer for email
    """
    if buyer_name not in df.columns:
        return None

    # Extract only the relevant columns for this buyer
    buyer_df = df[['Date', 'Time Block', buyer_name]].copy()

    # Get buyer name without "Demand Schedule" suffix for email body
    buyer_short_name = buyer_name.split("Demand Schedule")[0].strip()

    # Format the data for email
    html_table = buyer_df.to_html(index=False)

    # Create email body
    text_content = f"Dear Sir/Ma'am,\n\n"
    text_content += f"Please find attached the demand schedule for {buyer_short_name}.\n\n"

    return {
        'html': html_table,
        'text': text_content,
        'csv': buyer_df.to_csv(index=False),
        'buyer_short_name': buyer_short_name
    }

def send_email(recipient_email, subject, text_content, html_content=None, attachment_data=None, attachment_name=None):
    """
    Sends an email with optional HTML content and attachment
    """
    # Handle multiple recipients (comma-separated)
    recipients = [email.strip() for email in recipient_email.split(',')]

    msg = MIMEMultipart('alternative')
    msg['From'] = SENDER_EMAIL
    msg['To'] = recipient_email  # Keep original string for message header
    msg['Subject'] = subject

    # Attach text part
    part1 = MIMEText(text_content, 'plain')
    msg.attach(part1)

    # Attach HTML part if provided
    if html_content:
        part2 = MIMEText(html_content, 'html')
        msg.attach(part2)

    # Attach file if provided
    if attachment_data and attachment_name:
        attachment = MIMEApplication(attachment_data)
        attachment['Content-Disposition'] = f'attachment; filename="{attachment_name}"'
        msg.attach(attachment)

    # Try primary SMTP server first
    success = try_send_email(
        msg, recipients, SMTP_SERVER, SMTP_PORT, SMTP_USE_SSL, True,
        SENDER_EMAIL, SENDER_PASSWORD, recipient_email
    )

    # If primary server fails, try backup server
    if not success:
        print(f"Trying backup SMTP server for {recipient_email}...")
        success = try_send_email(
            msg, recipients, BACKUP_SMTP_SERVER, BACKUP_SMTP_PORT, BACKUP_SMTP_USE_SSL, True,
            SENDER_EMAIL, SENDER_PASSWORD, recipient_email
        )

    # If both fail, try local SMTP server without authentication as last resort
    if not success:
        print(f"Trying local SMTP server for {recipient_email} as last resort...")
        success = try_send_email(
            msg, recipients, LOCAL_SMTP_SERVER, LOCAL_SMTP_PORT, False, LOCAL_SMTP_USE_AUTH,
            SENDER_EMAIL, SENDER_PASSWORD, recipient_email
        )

    return success

def try_send_email(msg, recipients, smtp_server, smtp_port, use_ssl, use_auth, sender_email, sender_password, recipient_email_str):
    """
    Helper function to attempt sending an email with proper error handling
    """
    max_retries = 2
    retry_count = 0

    while retry_count < max_retries:
        try:
            # Create SMTP connection with timeout based on SSL setting
            if use_ssl:
                server = smtplib.SMTP_SSL(smtp_server, smtp_port, timeout=30)
            else:
                server = smtplib.SMTP(smtp_server, smtp_port, timeout=30)

            # Set debug level for troubleshooting
            server.set_debuglevel(1)

            # Use TLS if not using SSL and port is 587
            if not use_ssl and smtp_port == 587:
                server.starttls()

            # Login if authentication is required
            if use_auth:
                server.login(sender_email, sender_password)

            # Send message
            server.send_message(msg, sender_email, recipients)

            # Close connection
            server.quit()

            print(f"Email sent successfully to {recipient_email_str}")
            return True

        except smtplib.SMTPConnectError as e:
            print(f"SMTP Connection Error: {e}")
            retry_count += 1
            if retry_count < max_retries:
                print(f"Retrying ({retry_count}/{max_retries})...")

        except smtplib.SMTPAuthenticationError as e:
            print(f"SMTP Authentication Error: {e}")
            # No retry for auth errors
            return False

        except smtplib.SMTPException as e:
            print(f"SMTP Error: {e}")
            retry_count += 1
            if retry_count < max_retries:
                print(f"Retrying ({retry_count}/{max_retries})...")

        except OSError as e:
            print(f"Socket/OS Error: {e}")
            retry_count += 1
            if retry_count < max_retries:
                print(f"Retrying ({retry_count}/{max_retries})...")

        except Exception as e:
            print(f"Unexpected error sending to {recipient_email_str}: {e}")
            retry_count += 1
            if retry_count < max_retries:
                print(f"Retrying ({retry_count}/{max_retries})...")

    print(f"Failed to send email to {recipient_email_str} after {max_retries} attempts")
    return False

def process_and_send_email_to_buyer(buyer_name=None):
    """
    Process load data and send email to a specific buyer or all buyers
    """
    # Get today's load data
    load_df = get_load_data_for_today()
    if load_df is None:
        print("No data to process")
        return

    # Get buyer email addresses
    buyer_emails = get_buyer_emails()
    if not buyer_emails:
        print("No buyer email addresses found in the database")
        return

    # If a specific buyer is provided, only process that buyer
    if buyer_name:
        if buyer_name not in load_df.columns:
            print(f"Buyer '{buyer_name}' not found in today's data")
            return

        buyers_to_process = [buyer_name]
    else:
        # Process all buyers
        buyers_to_process = [col for col in load_df.columns if col not in ['Date', 'Time Block']]

    # Process each buyer
    emails_sent = 0
    for buyer in buyers_to_process:
        # Check if we have an email for this buyer
        if buyer not in buyer_emails:
            print(f"No email address found for buyer: {buyer}")
            continue

        # Format data for this buyer
        formatted_data = format_load_data_for_email(load_df, buyer)
        if not formatted_data:
            print(f"Could not format data for buyer: {buyer}")
            continue

        # Send email
        subject = f"Regarding {formatted_data['buyer_short_name']}"
        success = send_email(
            recipient_email=buyer_emails[buyer],
            subject=subject,
            text_content=formatted_data['text'],
            html_content=formatted_data['html'],
            attachment_data=formatted_data['csv'].encode('utf-8'),
            attachment_name=f"{formatted_data['buyer_short_name']}_demand_schedule_{datetime.date.today().strftime('%Y%m%d')}.csv"
        )

        if success:
            emails_sent += 1

    print(f"Emails sent: {emails_sent} of {len(buyers_to_process)}")

def process_and_send_emails():
    """
    Main function to process load data and send emails to all buyers
    """
    # Create email table if it doesn't exist
    create_email_table()

    # Process and send emails to all buyers
    process_and_send_email_to_buyer()

def add_buyer_email(buyer_name, email_address):
    """
    Adds or updates a buyer's email address in the database
    """
    conn = get_db_connection()
    cursor = conn.cursor()

    query = "REPLACE INTO buyer_emails (buyer_name, email_address) VALUES (%s, %s)"
    cursor.execute(query, (buyer_name, email_address))

    conn.commit()
    cursor.close()
    conn.close()
    print(f"Email address for {buyer_name} added/updated successfully")

# List all buyers with their email addresses
def list_buyer_emails():
    """
    Lists all buyers and their email addresses from the database
    """
    conn = get_db_connection()
    cursor = conn.cursor()

    # Get all buyers from load_data
    query_buyers = "SELECT DISTINCT buyer_name FROM load_data"
    cursor.execute(query_buyers)
    all_buyers = [row[0] for row in cursor.fetchall()]

    # Get all buyers with email addresses
    query_emails = "SELECT buyer_name, email_address FROM buyer_emails"
    cursor.execute(query_emails)
    email_dict = {row[0]: row[1] for row in cursor.fetchall()}

    cursor.close()
    conn.close()

    print("\nBuyer Email Addresses:")
    print("=" * 60)
    print(f"{'Buyer Name':<30} {'Email Address':<30}")
    print("-" * 60)

    for buyer in all_buyers:
        if buyer in ['Date', 'Time Block']:
            continue
        email = email_dict.get(buyer, "Not set")
        print(f"{buyer:<30} {email:<30}")

    print("=" * 60)

# Command-line interface
def print_help():
    """
    Prints help information for the command-line interface
    """
    print("\nUsage:")
    print("  python mail.py [command] [options]")
    print("\nCommands:")
    print("  send                  - Process today's data and send emails to all buyers")
    print("  send [buyer]          - Process today's data and send email to a specific buyer")
    print("  add [buyer] [email]   - Add or update email address for a buyer")
    print("  list                  - List all buyers and their email addresses")
    print("  help                  - Show this help message")
    print("\nExamples:")
    print("  python mail.py send")
    print("  python mail.py send \"Linde Ludhiana Demand Schedule\"")
    print("  python mail.py add \"Buyer Name\" <EMAIL>")
    print("  python mail.py list")

# Main execution
if __name__ == "__main__":
    import sys

    # Create email table if it doesn't exist
    create_email_table()

    if len(sys.argv) < 2:
        print_help()
        sys.exit(0)

    command = sys.argv[1].lower()

    if command == "send":
        if len(sys.argv) >= 3:
            # Send to specific buyer
            buyer_name = sys.argv[2]
            process_and_send_email_to_buyer(buyer_name)
        else:
            # Send to all buyers
            process_and_send_emails()

    elif command == "add" and len(sys.argv) >= 4:
        buyer_name = sys.argv[2]
        email_address = sys.argv[3]
        add_buyer_email(buyer_name, email_address)

    elif command == "list":
        list_buyer_emails()

    elif command == "help":
        print_help()

    else:
        print("Invalid command or missing arguments.")
        print_help()