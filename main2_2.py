import re
import datetime
import calendar
from datetime import timed<PERSON>ta
from ftplib import FTP
from io import BytesIO
import pandas as pd
import mysql.connector
import numpy as np
import streamlit as st

# -------------------------------
# FTP Configuration & Functions
# -------------------------------

FTP_HOST = "*************"
FTP_PORT = 21
FTP_USER = "partner"
FTP_PASS = "jEm9P6182x89"

# Remote FTP folder paths (Updated paths)
FTP_PATH_SCHEDULE = "/WIND/SCHEDULE/KA/Kudligi_HYB_FP/"
FTP_PATH_LOAD = "/WIND/Kudligi/IV_Partner/Load/"
FTP_PATH_OBLIGATION = "/WIND/Kudligi/IV_Partner/Obligation/"
FTP_PATH_GDAM_RTM_RATIO = "/WIND/Kudligi/IV_Partner/Obligation/GDAM_RTM_Ratio/"
FTP_PATH_FINAL_BUYER_SCHEDULE = "/WIND/SCHEDULE/KA/Kudligi_HYB_FP/Final_Buyer_Schedule/"

def connect_ftp():
    ftp = FTP()
    ftp.connect(FTP_HOST, FTP_PORT)
    ftp.login(FTP_USER, FTP_PASS)

    return ftp

def download_ftp_file(ftp, remote_folder, remote_filename):
    """
    Downloads a file from the FTP server and returns a BytesIO stream.
    """
    ftp.cwd(remote_folder)
    bio = BytesIO()
    try:
        ftp.retrbinary(f"RETR {remote_filename}", bio.write)
        bio.seek(0)
        return bio
    except Exception as e:
        st.error(f"Error downloading {remote_filename} from {remote_folder}: {e}")
        return None

def find_file_for_date(ftp, remote_folder, target_date, file_type='load'):
    """
    For Load and Obligation: target file is yyyymmdd.xlsx; if not available then yyyymmdd.csv.
    """
    ftp.cwd(remote_folder)
    target_filename_xlsx = target_date.strftime("%Y%m%d") + ".xlsx"
    target_filename_csv = target_date.strftime("%Y%m%d") + ".csv"

    files = ftp.nlst()
    if file_type in ['load', 'obligation']:
        if target_filename_xlsx in files:
            return target_filename_xlsx
        elif target_filename_csv in files:
            return target_filename_csv
    return None

def find_latest_modified_file(ftp, remote_folder, file_extensions=None):
    """
    Find the latest modified file in the given FTP folder.

    Args:
        ftp: FTP connection object
        remote_folder: Remote folder path
        file_extensions: List of allowed file extensions (e.g., ['.xlsx', '.csv'])
                        If None, all files are considered

    Returns:
        Filename of the latest modified file, or None if no files found
    """
    try:
        ftp.cwd(remote_folder)
    except Exception as e:
        st.error(f"FTP Error accessing {remote_folder}: {str(e)}")
        return None

    try:
        files = ftp.nlst()
        if not files:
            return None

        # Filter by file extensions if specified
        if file_extensions:
            files = [f for f in files if any(f.lower().endswith(ext.lower()) for ext in file_extensions)]

        if not files:
            return None

        # Get modification times for all files
        file_times = []
        for filename in files:
            try:
                # Get file modification time
                ftp.voidcmd("TYPE I")  # Set binary mode
                response = ftp.sendcmd(f"MDTM {filename}")
                # MDTM response format: "213 YYYYMMDDHHMMSS"
                time_str = response.split()[1]
                file_times.append((filename, time_str))
            except Exception:
                # If MDTM fails, skip this file
                continue

        if not file_times:
            # If MDTM is not supported, just return the first file
            return files[0]

        # Sort by modification time (newest first)
        file_times.sort(key=lambda x: x[1], reverse=True)
        return file_times[0][0]

    except Exception as e:
        st.error(f"Error finding latest file in {remote_folder}: {str(e)}")
        return None

def find_latest_revision_file(ftp, remote_folder, target_date, schedule_type="DA"):
    try:
        ftp.cwd(remote_folder)
    except Exception as e:
        st.error(f"FTP Error: {str(e)}")
        return None

    files = ftp.nlst()
    date_str = target_date.strftime("%d.%m.%Y")  # Changed to DD.MM.YYYY format

    # Modified patterns to match actual filenames
    if schedule_type.upper() == "DA":
        pattern = re.compile(
            r"Kudligi\s+HYB\s+FP_DA_" + re.escape(date_str) +
            r"_DA\s+RDA(\d+)\.csv",
            re.IGNORECASE
        )
    else:
        pattern = re.compile(
            r"Kudligi\s+HYB\s+FP_IntraDay_" + re.escape(date_str) +
            r"_IntraDay\s+RID(\d+)\.csv",
            re.IGNORECASE
        )

    max_revision = -1
    selected_file = None

    for f in files:
        m = pattern.search(f)
        if m:
            rev = int(m.group(1))
            if rev > max_revision:
                max_revision = rev
                selected_file = f

    return selected_file

# -------------------------------
# MySQL Connection & Table Creation
# -------------------------------

def get_db_connection():
    conn = mysql.connector.connect(
        host='***********',
        port=3306,                 # Explicitly specify the port
        user='root',               # Using root as specified
        password='271220021247@Admin',   # Your MySQL password
        database='FP2',            # Enable SSL as specified
        ssl_verify_cert=False,     # Don't verify server certificate
        use_pure=True              # Use pure Python implementation which supports modern authentication
    )
    return conn

def create_tables():
    conn = get_db_connection()
    cursor = conn.cursor()

    # Table for load data (note: "load_value" avoids reserved word conflict)
    create_load_table = """
    CREATE TABLE IF NOT EXISTS load_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        date DATE,
        block INT,
        buyer_name VARCHAR(100),
        load_value FLOAT
    );
    """
    cursor.execute(create_load_table)

    # Table for obligation data
    create_obligation_table = """
    CREATE TABLE IF NOT EXISTS obligation (
        id INT AUTO_INCREMENT PRIMARY KEY,
        date DATE,
        block INT,
        market FLOAT,
        UNIQUE KEY date_block_idx (date, block)
    );
    """
    cursor.execute(create_obligation_table)

    # Create two schedule tables: one for Solar and one for Wind.
    create_solar_schedule = """
    CREATE TABLE IF NOT EXISTS schedule_solar (
        id INT AUTO_INCREMENT PRIMARY KEY,
        date DATE,
        block INT,
        sch FLOAT,
        UNIQUE KEY date_block_idx (date, block)
    );
    """
    cursor.execute(create_solar_schedule)

    create_wind_schedule = """
    CREATE TABLE IF NOT EXISTS schedule_wind (
        id INT AUTO_INCREMENT PRIMARY KEY,
        date DATE,
        block INT,
        sch FLOAT,
        UNIQUE KEY date_block_idx (date, block)
    );
    """
    cursor.execute(create_wind_schedule)

    conn.commit()
    cursor.close()
    conn.close()

# Create GDAM-RTM Ratio table
def create_gdam_rtm_ratio_table():
    conn = get_db_connection()
    cursor = conn.cursor()

    create_gdam_rtm_ratio_table = """
    CREATE TABLE IF NOT EXISTS gdam_rtm_ratio (
        id INT AUTO_INCREMENT PRIMARY KEY,
        block INT,
        gdam FLOAT,
        rtm FLOAT,
        UNIQUE KEY block_idx (block)
    );
    """
    cursor.execute(create_gdam_rtm_ratio_table)

    # Check if table is empty and insert default values
    cursor.execute("SELECT COUNT(*) FROM gdam_rtm_ratio")
    count = cursor.fetchone()[0]

    if count == 0:
        # Insert default values where GDAM = 0.5 and RTM = 0.5 for all 96 blocks
        for block in range(1, 97):
            cursor.execute(
                "INSERT INTO gdam_rtm_ratio (block, gdam, rtm) VALUES (%s, %s, %s)",
                (block, 0.5, 0.5)
            )

    conn.commit()
    cursor.close()
    conn.close()

# Create configuration tables: contract_value, state, tariff_difference, buyer_mapping.
def create_config_tables():
    conn = get_db_connection()
    cursor = conn.cursor()

    create_contract_table = """
    CREATE TABLE IF NOT EXISTS contract_value (
        buyer_name VARCHAR(100) PRIMARY KEY,
        contract_value FLOAT
    );
    """
    cursor.execute(create_contract_table)

    create_state_table = """
    CREATE TABLE IF NOT EXISTS state (
        buyer_name VARCHAR(100) PRIMARY KEY,
        state VARCHAR(100)
    );
    """
    cursor.execute(create_state_table)

    create_tariff_table = """
    CREATE TABLE IF NOT EXISTS tariff_difference (
        block INT,
        state VARCHAR(100),
        tariff_difference FLOAT,
        PRIMARY KEY (block, state)
    );
    """
    cursor.execute(create_tariff_table)

    # Create buyer mapping table for Final Buyer Schedule
    create_buyer_mapping_table = """
    CREATE TABLE IF NOT EXISTS buyer_mapping (
        id INT AUTO_INCREMENT PRIMARY KEY,
        file_column_name VARCHAR(100) UNIQUE,
        actual_buyer_name VARCHAR(100),
        description VARCHAR(255)
    );
    """
    cursor.execute(create_buyer_mapping_table)

    # Insert default mappings if they don't exist
    check_query = "SELECT COUNT(*) FROM buyer_mapping"
    cursor.execute(check_query)
    count = cursor.fetchone()[0]

    if count == 0:
        # Insert default mappings
        default_mappings = [
            ("LINDE INDIA LIMITED A.P.", "Linde Sri City", "Linde Sri City Demand Schedule"),
            ("LINDEGJ62955", "Linde Dahej", "Linde Dahej Demand Schedule"),
            ("LINDEINDIA12", "Linde Selaqui", "Linde Selaqui Demand Schedule")
        ]

        for file_col, buyer, desc in default_mappings:
            insert_query = """
            INSERT INTO buyer_mapping (file_column_name, actual_buyer_name, description)
            VALUES (%s, %s, %s)
            """
            cursor.execute(insert_query, (file_col, buyer, desc))

    conn.commit()
    cursor.close()
    conn.close()

# Create CUF% table to store daily cumulative CUF% for each buyer.
def create_cuf_pct_table():
    conn = get_db_connection()
    cursor = conn.cursor()
    query = """
    CREATE TABLE IF NOT EXISTS cuf_pct (
        date DATE,
        buyer_name VARCHAR(100),
        cuf_pct FLOAT,
        PRIMARY KEY (date, buyer_name)
    );
    """
    cursor.execute(query)
    conn.commit()
    cursor.close()
    conn.close()

# -------------------------------
# Data Loading Function (Using FTP)
# -------------------------------

def find_final_buyer_schedule_file(ftp, remote_folder, target_date):
    """
    Find the final buyer schedule file for a specific date in the format dd-mm-yyyy.csv
    """
    try:
        ftp.cwd(remote_folder)
    except Exception as e:
        st.error(f"FTP Error: {str(e)}")
        return None

    files = ftp.nlst()
    date_str = target_date.strftime("%d-%m-%Y")  # Format as dd-mm-yyyy

    for f in files:
        if date_str in f and f.lower().endswith('.csv'):
            return f

    return None

def process_final_buyer_schedule(target_date=None, start_row=17, end_row=115):
    """
    Process the final buyer schedule file for a specific date and calculate CUF%

    Parameters:
    - target_date: The date to process (default: today)
    - start_row: The starting row number in the CSV file (1-based, default: 17)
    - end_row: The ending row number in the CSV file (1-based, default: 115)
    """
    if target_date is None:
        target_date = datetime.date.today()

    st.write(f"Processing final buyer schedule for {target_date}")

    # Convert to 0-based indexing for pandas
    # If header is at row 11 (0-based index 10), then:
    # Row 18 corresponds to index 7 (18-11)
    # Row 113 corresponds to index 102 (113-11)
    start_idx = start_row - 11  # Adjust for header at row 11
    end_idx = end_row - 11 + 1  # +1 because end index is exclusive in pandas

    # Connect to FTP and find the file
    ftp = connect_ftp()
    file_name = find_final_buyer_schedule_file(ftp, FTP_PATH_FINAL_BUYER_SCHEDULE, target_date)

    if not file_name:
        st.warning(f"No final buyer schedule file found for {target_date}")
        ftp.quit()
        return False

    # Download the file
    bio = download_ftp_file(ftp, FTP_PATH_FINAL_BUYER_SCHEDULE, file_name)
    ftp.quit()

    if not bio:
        st.error(f"Failed to download {file_name}")
        return False

    # Read the CSV file
    try:
        # Read the file with header at row 10 (11th row in the file)
        df = pd.read_csv(bio, header=10)

        # Basic file information
        st.write(f"Processing file: {file_name}")

        # Get the buyer mapping from the database
        conn = get_db_connection()
        buyer_mapping_df = pd.read_sql("SELECT file_column_name, actual_buyer_name FROM buyer_mapping", conn)

        # Create a dictionary for mapping
        buyer_mapping = dict(zip(buyer_mapping_df['file_column_name'], buyer_mapping_df['actual_buyer_name']))

        # Get contract values for CUF calculation
        contract_values_df = pd.read_sql("SELECT buyer_name, contract_value FROM contract_value", conn)
        contract_values = dict(zip(contract_values_df['buyer_name'], contract_values_df['contract_value']))

        # Calculate total energy for each buyer
        buyer_energy = {}

        # Process each mapped buyer column
        for file_col, buyer_name in buyer_mapping.items():
            if file_col in df.columns:
                # Sum the energy values using the specified row range
                energy_values = df.iloc[start_idx:end_idx][file_col].astype(float)

                # Calculate sum
                total_energy = energy_values.sum()
                buyer_energy[buyer_name] = total_energy

                st.write(f"Total energy for {buyer_name}: {total_energy:.2f}")
            else:
                st.warning(f"Column '{file_col}' not found in the file")

        # Calculate CUF% for each buyer
        cuf_pct = {}
        for buyer, energy in buyer_energy.items():
            if buyer in contract_values and contract_values[buyer] > 0:
                # Calculate CUF%
                contract_val = contract_values[buyer]
                days_in_month = calendar.monthrange(target_date.year, target_date.month)[1]
                denominator = contract_val * 96 * days_in_month
                daily_cuf_pct = (energy / denominator) * 100 if denominator > 0 else 0
                cuf_pct[buyer] = daily_cuf_pct
                st.write(f"Daily CUF% for {buyer}: {daily_cuf_pct:.2f}%")
            else:
                st.warning(f"No contract value found for {buyer}")

        # Get previous day's cumulative CUF%
        prev_day = target_date - timedelta(days=1)
        prev_cuf_query = f"SELECT buyer_name, cuf_pct FROM cuf_pct WHERE date = '{prev_day}'"
        prev_cuf_df = pd.read_sql(prev_cuf_query, conn)

        # If no previous day data, try to get the most recent data
        if prev_cuf_df.empty:
            prev_cuf_query = f"""
                SELECT buyer_name, cuf_pct
                FROM cuf_pct
                WHERE date = (
                    SELECT MAX(date)
                    FROM cuf_pct
                    WHERE date < '{target_date}'
                )
            """
            prev_cuf_df = pd.read_sql(prev_cuf_query, conn)

        prev_cuf_pct = dict(zip(prev_cuf_df['buyer_name'], prev_cuf_df['cuf_pct'])) if not prev_cuf_df.empty else {}

        # Calculate cumulative CUF%
        cumulative_cuf_pct = {}
        for buyer, daily_pct in cuf_pct.items():
            # For the first day of the month, start fresh
            if target_date.day == 1:
                cumulative_cuf_pct[buyer] = daily_pct
            else:
                # Add previous day's CUF% to accumulate over days
                prev_cuf = prev_cuf_pct.get(buyer, 0)  # Get last day's CUF or 0
                cumulative_cuf_pct[buyer] = prev_cuf + daily_pct

        # Update the CUF% table
        cursor = conn.cursor()
        for buyer, pct in cumulative_cuf_pct.items():
            query = "REPLACE INTO cuf_pct (date, buyer_name, cuf_pct) VALUES (%s, %s, %s)"
            cursor.execute(query, (target_date, buyer, float(pct)))

        conn.commit()
        cursor.close()
        conn.close()

        st.success(f"Successfully processed final buyer schedule for {target_date}")
        return True

    except Exception as e:
        st.error(f"Error processing final buyer schedule: {str(e)}")
        return False

def load_data_to_mysql():
    # Define target dates: current date and current date + 1.
    target_dates = [datetime.date.today(), datetime.date.today() + timedelta(days=1)]

    conn = get_db_connection()
    cursor = conn.cursor()

    ftp = connect_ftp()

    for target_date in target_dates:
        # 1. Process Load folder.
        load_filename = find_file_for_date(ftp, FTP_PATH_LOAD, target_date, file_type='load')
        if load_filename:
            bio = download_ftp_file(ftp, FTP_PATH_LOAD, load_filename)
            if bio:
                if load_filename.lower().endswith(".xlsx"):
                    df = pd.read_excel(bio)
                else:
                    df = pd.read_csv(bio)

                # Clear existing entries for this date before inserting new ones.
                delete_query = "DELETE FROM load_data WHERE date = %s"
                cursor.execute(delete_query, (target_date,))
                conn.commit()

                # Process each row with upsert logic.
                # Note: We now force the date value to the target_date rather than reading from file.
                for _, row in df.iterrows():
                    # Use target_date as the date value.
                    date_val = target_date

                    block = row.get('Time Block', None)
                    if pd.isna(block):
                        block = 0  # Or another appropriate default.
                    try:
                        block = int(block)
                    except Exception:
                        block = 0

                    # Process each buyer column (ignoring Date and Time Block)
                    for col in df.columns:
                        if col not in ['Date', 'Time Block']:
                            # Force buyer_name to a string. If the column name is empty or "nan", replace with "0".
                            buyer_name = str(col).strip()
                            if buyer_name == "" or buyer_name.lower() == "nan":
                                buyer_name = "0"

                            load_value = row[col]
                            if pd.isna(load_value):
                                load_value = 0

                            query = """INSERT INTO load_data (date, block, buyer_name, load_value)
                                       VALUES (%s, %s, %s, %s)
                                       ON DUPLICATE KEY UPDATE load_value = VALUES(load_value)"""
                            cursor.execute(query, (date_val, block, buyer_name, load_value))
                conn.commit()
                st.success(f"Load data from file '{load_filename}' inserted/updated successfully.")

        # 2. Process Obligation folder.
        # Here we force date_val to target_date as well.
        obligation_filename = find_file_for_date(ftp, FTP_PATH_OBLIGATION, target_date, file_type='obligation')
        if obligation_filename:
            bio = download_ftp_file(ftp, FTP_PATH_OBLIGATION, obligation_filename)
            if bio:
                df = pd.read_excel(bio)
                for _, row in df.iterrows():
                    # Instead of using the file's date, we use the target_date.
                    date_val = target_date
                    block = row.get('Time Block', row.get('Block', None))
                    if pd.isna(block):
                        block = 0
                    try:
                        block = int(block)
                    except Exception:
                        block = 0
                    market = row.get('Market', 0)
                    query = "REPLACE INTO obligation (date, block, market) VALUES (%s, %s, %s)"
                    cursor.execute(query, (date_val, block, market))
                conn.commit()
                st.success(f"Obligation data from file '{obligation_filename}' inserted successfully.")
        else:
            # Check if data already exists for this date
            check_query = "SELECT COUNT(*) FROM obligation WHERE date = %s"
            cursor.execute(check_query, (target_date,))
            count = cursor.fetchone()[0]

            if count > 0:
                st.warning(f"Obligation file for date '{target_date.strftime('%Y%m%d')}' not found in FTP folder {FTP_PATH_OBLIGATION}. Using existing data in database.")
                st.success(f"Existing obligation data for date '{target_date}' preserved.")
            else:
                st.warning(f"Obligation file for date '{target_date.strftime('%Y%m%d')}' not found in FTP folder {FTP_PATH_OBLIGATION}. No existing data found. Inserting default obligation data.")
                for block in range(1, 97):
                    query = "REPLACE INTO obligation (date, block, market) VALUES (%s, %s, %s)"
                    cursor.execute(query, (target_date, block, 0))
                conn.commit()
                st.success(f"Default obligation data for date '{target_date}' inserted successfully.")

        # 3. Process Solar Schedule.
        # Check if data already exists for this date
        check_query = "SELECT COUNT(*) FROM schedule_solar WHERE date = %s"
        cursor.execute(check_query, (target_date,))
        count = cursor.fetchone()[0]

        if count > 0:
            st.success(f"Existing solar schedule for date '{target_date}' preserved.")
        else:
            # No existing data, insert default values
            time_blocks = list(range(1, 97))
            solar_data = []
            for block in time_blocks:
                solar_data.append({
                    "Date": target_date,
                    "Time Block": block,
                    "Sch": 0
                })
            df_solar = pd.DataFrame(solar_data)
            for _, row in df_solar.iterrows():
                query = "REPLACE INTO schedule_solar (date, block, sch) VALUES (%s, %s, %s)"
                cursor.execute(query, (row['Date'], row['Time Block'], row['Sch']))
            conn.commit()
            st.success(f"Default solar schedule for date '{target_date}' inserted successfully.")

        # 4. Process Wind Schedule.
        # Determine schedule type: 'DA' (Day Ahead) if target_date is after today; else 'IntraDay'
        schedule_type = "DA" if target_date > datetime.date.today() else "IntraDay"
        st.write(f"Processing {target_date} as {schedule_type} schedule")
        wind_filename = find_latest_revision_file(ftp, FTP_PATH_SCHEDULE, target_date, schedule_type)
        if wind_filename:
            bio = download_ftp_file(ftp, FTP_PATH_SCHEDULE, wind_filename)
            if bio:
                try:
                    if wind_filename.lower().endswith('.csv'):
                        df = pd.read_csv(bio, header=None, skiprows=11, nrows=96)
                    elif wind_filename.lower().endswith(('.xlsx', '.xls')):
                        df = pd.read_excel(bio, header=None, skiprows=11, nrows=96, engine='openpyxl')
                    else:
                        st.error(f"Unsupported file format: {wind_filename}")
                        return

                    # Select specific columns: Column 0 -> 'Block', Column 1 -> 'Date', Column 4 -> 'Sch'
                    df = df.iloc[:, [0, 1, 4]]
                    df.columns = ['Block', 'Date', 'Sch']
                    # Force the date to target_date (if needed)
                    df['Date'] = target_date
                    df['Sch'].fillna(0, inplace=True)

                    cursor.execute("DELETE FROM schedule_wind WHERE date = %s", (target_date,))
                    conn.commit()

                    data_tuples = [(row['Date'], row['Block'], row['Sch']) for _, row in df.iterrows()]
                    cursor.executemany(
                        """REPLACE INTO schedule_wind (date, block, sch)
                        VALUES (%s, %s, %s)""",
                        data_tuples
                    )
                    conn.commit()
                    st.success(f"Wind schedule ({schedule_type}) data from '{wind_filename}' updated successfully.")

                except Exception as e:
                    st.error(f"Error processing {wind_filename}: {str(e)}")
                    conn.rollback()
        else:
            # Check if data already exists for this date
            check_query = "SELECT COUNT(*) FROM schedule_wind WHERE date = %s"
            cursor.execute(check_query, (target_date,))
            count = cursor.fetchone()[0]

            if count > 0:
                st.warning(f"{schedule_type} wind schedule file for date '{target_date}' not found. Using existing data in database.")
                st.success(f"Existing {schedule_type} wind schedule for date '{target_date}' preserved.")
            else:
                st.warning(f"{schedule_type} wind schedule file for date '{target_date}' not found. No existing data found. Inserting default schedule.")
                default_data = [(target_date, block, 0) for block in range(1, 97)]
                cursor.executemany(
                    "REPLACE INTO schedule_wind (date, block, sch) VALUES (%s, %s, %s)",
                    default_data
                )
                conn.commit()
                st.success(f"Default {schedule_type} wind schedule for date '{target_date}' inserted successfully.")

    cursor.close()
    conn.close()

    # 5. Load GDAM-RTM Ratio data from FTP
    st.write("### Loading GDAM-RTM Ratio Data")
    try:
        # Find the latest modified file in the GDAM_RTM_Ratio folder
        latest_file = find_latest_modified_file(ftp, FTP_PATH_GDAM_RTM_RATIO, ['.xlsx', '.csv'])

        if latest_file:
            st.info(f"Found GDAM-RTM ratio file: {latest_file}")

            # Download the file
            bio = download_ftp_file(ftp, FTP_PATH_GDAM_RTM_RATIO, latest_file)

            if bio:
                # Read the file based on extension
                if latest_file.lower().endswith('.xlsx'):
                    df = pd.read_excel(bio)
                else:
                    df = pd.read_csv(bio)

                # Validate and process the file
                expected_columns = ['Time Block', 'GDAM', 'RTM']

                # Create a mapping from expected columns to actual columns (case-insensitive)
                column_mapping = {}
                for expected_col in expected_columns:
                    expected_lower = expected_col.lower()
                    found = False
                    for actual_col in df.columns:
                        if actual_col.lower().strip() == expected_lower:
                            column_mapping[expected_col] = actual_col
                            found = True
                            break
                    if not found:
                        st.error(f"Required column '{expected_col}' not found in GDAM-RTM ratio file. Available columns: {list(df.columns)}")
                        ftp.quit()
                        return

                # Rename columns to standard names
                df = df.rename(columns={v: k for k, v in column_mapping.items()})

                # Validate and clean data
                df['Time Block'] = pd.to_numeric(df['Time Block'], errors='coerce')
                df['GDAM'] = pd.to_numeric(df['GDAM'], errors='coerce')
                df['RTM'] = pd.to_numeric(df['RTM'], errors='coerce')

                # Remove invalid rows
                df = df.dropna(subset=['Time Block', 'GDAM', 'RTM'])
                df = df[(df['Time Block'] >= 1) & (df['Time Block'] <= 96)]
                df['Time Block'] = df['Time Block'].astype(int)

                # Remove duplicates, keeping last
                df = df.drop_duplicates(subset=['Time Block'], keep='last')

                # Validate that GDAM + RTM = 1 for each block (with some tolerance)
                df['sum_check'] = df['GDAM'] + df['RTM']
                invalid_sums = df[abs(df['sum_check'] - 1.0) > 0.001]
                if not invalid_sums.empty:
                    st.warning(f"Warning: {len(invalid_sums)} blocks have GDAM + RTM ≠ 1.0")

                # Remove the sum_check column
                df = df.drop(columns=['sum_check'])

                if not df.empty:
                    # Load data into database
                    conn = get_db_connection()
                    cursor = conn.cursor()

                    # Clear existing data
                    cursor.execute("DELETE FROM gdam_rtm_ratio")
                    conn.commit()

                    # Insert new data
                    success_count = 0
                    for _, row in df.iterrows():
                        try:
                            query = "INSERT INTO gdam_rtm_ratio (block, gdam, rtm) VALUES (%s, %s, %s)"
                            cursor.execute(query, (int(row['Time Block']), float(row['GDAM']), float(row['RTM'])))
                            success_count += 1
                        except Exception as e:
                            st.error(f"Error inserting GDAM-RTM ratio for block {row['Time Block']}: {str(e)}")

                    # Ensure all 96 blocks exist with default values if missing
                    cursor.execute("SELECT block FROM gdam_rtm_ratio")
                    existing_blocks = set(row[0] for row in cursor.fetchall())
                    missing_blocks = set(range(1, 97)) - existing_blocks

                    if missing_blocks:
                        for block in missing_blocks:
                            cursor.execute(
                                "INSERT INTO gdam_rtm_ratio (block, gdam, rtm) VALUES (%s, %s, %s)",
                                (block, 0.5, 0.5)
                            )
                        success_count += len(missing_blocks)

                    conn.commit()
                    cursor.close()
                    conn.close()

                    st.success(f"Successfully loaded {success_count} GDAM-RTM ratio records from {latest_file}")
                else:
                    st.error("No valid GDAM-RTM ratio data found in file")
            else:
                st.error(f"Failed to download GDAM-RTM ratio file: {latest_file}")
        else:
            st.warning("No GDAM-RTM ratio files found in FTP folder. Using existing database values.")

    except Exception as e:
        st.error(f"Error loading GDAM-RTM ratio data: {str(e)}")

    ftp.quit()

def load_gdam_rtm_ratio():
    """
    Load GDAM-RTM ratio data from the latest modified file in the FTP folder.
    Expected file format:
    - Columns: Time Block, GDAM, RTM
    - 96 rows for time blocks 1-96
    - File extensions: .xlsx or .csv
    """
    st.subheader("Load GDAM-RTM Ratio Data from FTP")

    try:
        # Connect to FTP
        ftp = connect_ftp()

        # Find the latest modified file in the GDAM_RTM_Ratio folder
        latest_file = find_latest_modified_file(ftp, FTP_PATH_GDAM_RTM_RATIO, ['.xlsx', '.csv'])

        if not latest_file:
            st.error("No GDAM-RTM ratio files found in the FTP folder.")
            ftp.quit()
            return False

        st.info(f"Found latest file: {latest_file}")

        # Download the file
        bio = download_ftp_file(ftp, FTP_PATH_GDAM_RTM_RATIO, latest_file)
        ftp.quit()

        if not bio:
            st.error(f"Failed to download {latest_file}")
            return False

        # Read the file based on extension
        try:
            if latest_file.lower().endswith('.xlsx'):
                df = pd.read_excel(bio)
            else:
                df = pd.read_csv(bio)
        except Exception as e:
            st.error(f"Error reading file {latest_file}: {str(e)}")
            return False

        # Validate the file structure
        expected_columns = ['Time Block', 'GDAM', 'RTM']

        # Create a mapping from expected columns to actual columns
        column_mapping = {}
        for expected_col in expected_columns:
            expected_lower = expected_col.lower()
            found = False
            for actual_col in df.columns:
                if actual_col.lower().strip() == expected_lower:
                    column_mapping[expected_col] = actual_col
                    found = True
                    break
            if not found:
                st.error(f"Required column '{expected_col}' not found in file. Available columns: {list(df.columns)}")
                return False

        # Rename columns to standard names
        df = df.rename(columns={v: k for k, v in column_mapping.items()})

        # Validate data
        if df.empty:
            st.error("File is empty.")
            return False

        # Check for required columns after renaming
        missing_cols = [col for col in expected_columns if col not in df.columns]
        if missing_cols:
            st.error(f"Missing columns after processing: {missing_cols}")
            return False

        # Validate Time Block column
        df['Time Block'] = pd.to_numeric(df['Time Block'], errors='coerce')
        invalid_blocks = df[df['Time Block'].isna() | (df['Time Block'] < 1) | (df['Time Block'] > 96)]
        if not invalid_blocks.empty:
            st.error(f"Invalid time blocks found. Time blocks must be integers between 1 and 96.")
            st.dataframe(invalid_blocks)
            return False

        # Convert Time Block to integer
        df['Time Block'] = df['Time Block'].astype(int)

        # Validate GDAM and RTM columns
        df['GDAM'] = pd.to_numeric(df['GDAM'], errors='coerce')
        df['RTM'] = pd.to_numeric(df['RTM'], errors='coerce')

        invalid_values = df[df['GDAM'].isna() | df['RTM'].isna()]
        if not invalid_values.empty:
            st.error("Invalid GDAM or RTM values found. All values must be numeric.")
            st.dataframe(invalid_values)
            return False

        # Validate that GDAM and RTM are between 0 and 1
        invalid_range = df[(df['GDAM'] < 0) | (df['GDAM'] > 1) | (df['RTM'] < 0) | (df['RTM'] > 1)]
        if not invalid_range.empty:
            st.warning("GDAM and RTM values should be between 0 and 1.")
            st.dataframe(invalid_range)

        # Validate that GDAM + RTM = 1 for each block (with some tolerance)
        df['sum_check'] = df['GDAM'] + df['RTM']
        invalid_sums = df[abs(df['sum_check'] - 1.0) > 0.001]
        if not invalid_sums.empty:
            st.warning("Warning: GDAM + RTM should equal 1.0 for each block. The following blocks have invalid sums:")
            st.dataframe(invalid_sums[['Time Block', 'GDAM', 'RTM', 'sum_check']])

        # Remove the sum_check column
        df = df.drop(columns=['sum_check'])

        # Check for duplicate time blocks
        duplicates = df.duplicated(subset=['Time Block'], keep=False)
        if duplicates.any():
            st.warning("Duplicate time blocks found. Keeping only the last entry for each block.")
            df = df.drop_duplicates(subset=['Time Block'], keep='last')

        # Show preview of data
        st.write("Preview of data to be loaded:")
        st.dataframe(df.head(10))
        st.write(f"Total rows: {len(df)}")

        # Load data into database
        conn = get_db_connection()
        cursor = conn.cursor()

        # Clear existing data
        cursor.execute("DELETE FROM gdam_rtm_ratio")
        conn.commit()

        # Insert new data
        success_count = 0
        for _, row in df.iterrows():
            try:
                query = "INSERT INTO gdam_rtm_ratio (block, gdam, rtm) VALUES (%s, %s, %s)"
                cursor.execute(query, (int(row['Time Block']), float(row['GDAM']), float(row['RTM'])))
                success_count += 1
            except Exception as e:
                st.error(f"Error inserting row for block {row['Time Block']}: {str(e)}")

        conn.commit()
        cursor.close()
        conn.close()

        st.success(f"Successfully loaded {success_count} GDAM-RTM ratio records from {latest_file}")

        # Ensure all 96 blocks exist with default values if missing
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check which blocks are missing
        cursor.execute("SELECT block FROM gdam_rtm_ratio")
        existing_blocks = set(row[0] for row in cursor.fetchall())
        missing_blocks = set(range(1, 97)) - existing_blocks

        if missing_blocks:
            st.info(f"Adding default values for missing blocks: {sorted(missing_blocks)}")
            for block in missing_blocks:
                cursor.execute(
                    "INSERT INTO gdam_rtm_ratio (block, gdam, rtm) VALUES (%s, %s, %s)",
                    (block, 0.5, 0.5)
                )
            conn.commit()
            st.success(f"Added default values (0.5, 0.5) for {len(missing_blocks)} missing blocks.")

        cursor.close()
        conn.close()

        return True

    except Exception as e:
        st.error(f"Error loading GDAM-RTM ratio data: {str(e)}")
        return False

# -------------------------------
# Manual Data Entry Functions
# -------------------------------

def display_table(table_name):
    conn = get_db_connection()
    query = f"SELECT * FROM {table_name}"
    df = pd.read_sql(query, conn)
    conn.close()
    return df

def ensure_complete_blocks_for_load(df_wide, dates, buyer_columns):
    """Ensure that each date has all 96 time blocks (1-96) for load data.
    If any blocks are missing, they will be added with default values of 0 for all buyers."""
    complete_df = df_wide.copy()

    for date in dates:
        # Get all blocks for this date
        date_blocks = complete_df[complete_df['date'] == date]['block'].unique() if not complete_df.empty else []

        # Find missing blocks
        all_blocks = set(range(1, 97))
        existing_blocks = set(date_blocks)
        missing_blocks = all_blocks - existing_blocks

        # Add missing blocks with default values
        if missing_blocks:
            new_rows = []
            for block in missing_blocks:
                new_row = {'date': date, 'block': block}
                for buyer in buyer_columns:
                    new_row[buyer] = 0.0
                new_rows.append(new_row)

            # Use pd.concat instead of append (which is deprecated)
            if new_rows:
                new_df = pd.DataFrame(new_rows)
                complete_df = pd.concat([complete_df, new_df], ignore_index=True)

    return complete_df

def cleanup_duplicate_load_data():
    """Clean up duplicate date-block-buyer combinations in the load_data table."""
    conn = get_db_connection()
    cursor = conn.cursor()

    # First, identify all date-block-buyer combinations that have duplicates
    query = """
    SELECT date, block, buyer_name, COUNT(*) as count
    FROM load_data
    GROUP BY date, block, buyer_name
    HAVING COUNT(*) > 1
    """
    cursor.execute(query)
    duplicates = cursor.fetchall()

    for date, block, buyer_name, _ in duplicates:  # Using _ to ignore the count variable
        # Get all duplicate rows, ordered by id (to keep the most recent entry)
        query = "SELECT id FROM load_data WHERE date = %s AND block = %s AND buyer_name = %s ORDER BY id DESC"
        cursor.execute(query, (date, block, buyer_name))
        ids = [row[0] for row in cursor.fetchall()]

        # Keep only the first id (most recent due to DESC ordering)
        if len(ids) > 1:
            ids_to_delete = ids[1:]
            placeholders = ", ".join([str(id) for id in ids_to_delete])
            query = f"DELETE FROM load_data WHERE id IN ({placeholders})"
            cursor.execute(query)
            conn.commit()

    cursor.close()
    conn.close()

def manual_fill_load_data():
    st.subheader("Manual Data Entry: Load Data")

    # Clean up any duplicate entries in the load_data table
    cleanup_duplicate_load_data()

    df = display_table("load_data")
    today = datetime.date.today()
    tomorrow = today + datetime.timedelta(days=1)

    if not df.empty:
        df_wide = df.pivot_table(
            index=['date', 'block'],
            columns='buyer_name',
            values='load_value',
            aggfunc='first'
        ).reset_index()
    else:
        df_wide = pd.DataFrame(columns=['date', 'block'])

    if 'date' in df_wide.columns:
        df_wide['date'] = pd.to_datetime(df_wide['date']).dt.date
        df_wide = df_wide[df_wide['date'].isin([today, tomorrow])]

    # Get all buyer columns
    buyer_columns = [col for col in df_wide.columns if col not in ['date', 'block']]

    # Ensure all 96 blocks exist for today and tomorrow
    if not df_wide.empty:
        df_wide = ensure_complete_blocks_for_load(df_wide, [today, tomorrow], buyer_columns)

    # Add a helper for clipboard paste
    st.write("#### Quick Paste Helper")
    st.write("If you're having trouble pasting multiple rows directly into the table below, use this helper:")

    col1, col2, col3 = st.columns(3)
    with col1:
        paste_start_block = st.number_input("Starting Block Number", min_value=1, max_value=96, value=1, step=1, key="load_start_block")
    with col2:
        paste_date = st.date_input("Date for Pasted Values", value=today, key="load_date")
    with col3:
        buyer_options = [""] + buyer_columns if buyer_columns else [""]
        paste_buyer = st.selectbox("Buyer Name", options=buyer_options, key="load_buyer")

    paste_values = st.text_area("Paste Load Values (one per line)", height=100, key="load_values")

    if st.button("Apply Pasted Values", key="load_apply"):
        if paste_values.strip() and paste_buyer:
            try:
                # Parse the pasted values
                values = [float(v.strip()) for v in paste_values.strip().split('\n') if v.strip()]

                # Create a dataframe with the pasted values
                blocks = range(paste_start_block, paste_start_block + len(values))
                if max(blocks) > 96:
                    st.warning(f"Some values exceed block 96 and will be ignored.")
                    blocks = range(paste_start_block, min(paste_start_block + len(values), 97))
                    values = values[:len(blocks)]

                # Insert the data into the database
                conn = get_db_connection()
                cursor = conn.cursor()

                # For each block, either update existing record or insert new one
                for block, value in zip(blocks, values):
                    # Check if this specific block exists for this date and buyer
                    check_block_query = "SELECT id FROM load_data WHERE date = %s AND block = %s AND buyer_name = %s"
                    cursor.execute(check_block_query, (paste_date, block, paste_buyer))
                    result = cursor.fetchone()

                    if result:
                        # Update existing record
                        update_query = "UPDATE load_data SET load_value = %s WHERE date = %s AND block = %s AND buyer_name = %s"
                        cursor.execute(update_query, (value, paste_date, block, paste_buyer))
                    else:
                        # Insert new record
                        insert_query = "INSERT INTO load_data (date, block, buyer_name, load_value) VALUES (%s, %s, %s, %s)"
                        cursor.execute(insert_query, (paste_date, block, paste_buyer, value))

                conn.commit()
                cursor.close()
                conn.close()

                st.success(f"Successfully applied {len(values)} pasted values for {paste_buyer} starting from block {paste_start_block}.")

                # Force a rerun of the app to refresh the data
                st.rerun()
            except Exception as e:
                st.error(f"Error processing pasted values: {str(e)}")
        elif not paste_buyer:
            st.error("Please select a buyer name.")
        else:
            st.error("Please enter values to paste.")

    st.write("---")
    st.write("#### Edit Data Directly")

    edited_df = st.data_editor(df_wide, num_rows="dynamic", use_container_width=True)

    # Handle duplicate date-block combinations by keeping only the last entry
    if not edited_df.empty:
        # Check for duplicates in the edited dataframe
        duplicates = edited_df.duplicated(subset=['date', 'block'], keep=False)
        if duplicates.any():
            # Keep only the last entry for each date-block combination
            edited_df = edited_df.drop_duplicates(subset=['date', 'block'], keep='last')

        # Ensure blocks are between 1 and 96
        if 'block' in edited_df.columns:
            edited_df['block'] = edited_df['block'].astype(int)
            invalid_blocks = edited_df[(edited_df['block'] < 1) | (edited_df['block'] > 96)]
            if not invalid_blocks.empty:
                st.error(f"Time blocks must be between 1 and 96. Please fix these rows:\n{invalid_blocks}")
                return

        # Ensure all 96 blocks exist for each date
        buyer_columns = [col for col in edited_df.columns if col not in ['date', 'block']]
        unique_dates = edited_df['date'].unique()
        edited_df = ensure_complete_blocks_for_load(edited_df, unique_dates, buyer_columns)

    conn = get_db_connection()
    cursor = conn.cursor()

    # Delete only today's and tomorrow's data
    cursor.execute("DELETE FROM load_data WHERE date IN (%s, %s)", (today, tomorrow))
    conn.commit()

    buyer_columns = [col for col in edited_df.columns if col not in ['date', 'block']]
    df_long = edited_df.melt(
        id_vars=['date', 'block'],
        value_vars=buyer_columns,
        var_name='buyer_name',
        value_name='load_value'
    ).dropna(subset=['load_value'])

    for _, row in df_long.iterrows():
        # Convert numpy.float64 to Python float
        load_value = float(row["load_value"]) if row["load_value"] is not None else 0.0
        query = "REPLACE INTO load_data (date, block, buyer_name, load_value) VALUES (%s, %s, %s, %s)"
        cursor.execute(query, (row["date"], row["block"], row["buyer_name"], load_value))

    conn.commit()
    cursor.close()
    conn.close()
    st.success("Load data updated successfully.")

def ensure_complete_blocks(df, dates):
    """Ensure that each date has all 96 time blocks (1-96).
    If any blocks are missing, they will be added with a default value of 0."""
    complete_df = df.copy()

    for date in dates:
        # Get all blocks for this date
        date_blocks = complete_df[complete_df['date'] == date]['block'].unique()

        # Find missing blocks
        all_blocks = set(range(1, 97))
        existing_blocks = set(date_blocks)
        missing_blocks = all_blocks - existing_blocks

        # Add missing blocks with default values
        if missing_blocks:
            new_rows = []
            for block in missing_blocks:
                if 'market' in complete_df.columns:  # For obligation table
                    new_rows.append({'date': date, 'block': block, 'market': 0.0})
                elif 'sch' in complete_df.columns:  # For schedule tables
                    new_rows.append({'date': date, 'block': block, 'sch': 0.0})

            # Use pd.concat instead of append (which is deprecated)
            if new_rows:
                new_df = pd.DataFrame(new_rows)
                complete_df = pd.concat([complete_df, new_df], ignore_index=True)

    return complete_df

def cleanup_duplicate_dates(table_name, date_column="date", block_column="block"):
    """Clean up duplicate date ranges in the specified table.
    This function will keep only one set of blocks 1-96 for each date."""
    conn = get_db_connection()
    cursor = conn.cursor()

    # First, identify all dates in the table
    query = f"SELECT DISTINCT {date_column} FROM {table_name}"
    cursor.execute(query)
    dates = [row[0] for row in cursor.fetchall()]

    for date in dates:
        # For each date, check if there are duplicate blocks
        query = f"SELECT COUNT(*) FROM {table_name} WHERE {date_column} = %s"
        cursor.execute(query, (date,))
        count = cursor.fetchone()[0]

        # If there are more than 96 blocks for this date, there are duplicates
        if count > 96:
            st.warning(f"Found {count} blocks for date {date} in {table_name} table. Cleaning up duplicates...")

            # Get all rows for this date, ordered by id (to keep the most recent entries)
            query = f"SELECT id, {block_column} FROM {table_name} WHERE {date_column} = %s ORDER BY id DESC"
            cursor.execute(query, (date,))
            rows = cursor.fetchall()

            # Keep track of blocks we've seen
            seen_blocks = set()
            ids_to_keep = []

            # Keep only the first occurrence of each block (which is the most recent due to DESC ordering)
            for id, block in rows:
                if block not in seen_blocks:
                    seen_blocks.add(block)
                    ids_to_keep.append(id)

            # Delete all rows for this date except the ones we want to keep
            if ids_to_keep:
                placeholders = ", ".join([str(id) for id in ids_to_keep])
                query = f"DELETE FROM {table_name} WHERE {date_column} = %s AND id NOT IN ({placeholders})"
                cursor.execute(query, (date,))
                conn.commit()
                st.success(f"Cleaned up duplicate blocks for date {date} in {table_name} table.")

    cursor.close()
    conn.close()

def manual_fill_obligation_data():
    st.subheader("Obligation Data")

    # Clean up any duplicate date ranges in the obligation table
    cleanup_duplicate_dates("obligation")


    df = display_table("obligation")
    today = datetime.date.today()
    tomorrow = today + datetime.timedelta(days=1)

    if df.empty:
        df = pd.DataFrame(columns=["date", "block", "market"])
    else:
        df['date'] = pd.to_datetime(df['date']).dt.date
        df = df[df['date'].isin([today, tomorrow])]

    # Ensure all 96 blocks exist for today and tomorrow
    if not df.empty:
        df = ensure_complete_blocks(df, [today, tomorrow])
        # Sort by date and then by block number
        df = df.sort_values(by=['date', 'block'])

    st.subheader("Edit Obligation Data")
    df = df.drop(columns=['id'], errors='ignore')

    # Configure the data editor with column configurations to better handle clipboard operations
    column_config = {
        "date": st.column_config.DateColumn(
            "Date",
            help="The date for this obligation entry",
            format="YYYY-MM-DD",
        ),
        "block": st.column_config.NumberColumn(
            "Block",
            help="Time block (1-96)",
            min_value=1,
            max_value=96,
            step=1,
            format="%d"
        ),
        "market": st.column_config.NumberColumn(
            "Market",
            help="Market value",
            format="%.1f"
        )
    }

    # Add a helper for clipboard paste
    st.write("#### Quick Paste Helper")
    st.write("If you're having trouble pasting multiple rows directly into the table below, use this helper:")

    col1, col2 = st.columns(2)
    with col1:
        paste_start_block = st.number_input("Starting Block Number", min_value=1, max_value=96, value=1, step=1)
    with col2:
        paste_date = st.date_input("Date for Pasted Values", value=today)

    paste_values = st.text_area("Paste Market Values (one per line)", height=100)

    if st.button("Apply Pasted Values"):
        if paste_values.strip():
            try:
                # Parse the pasted values
                values = [float(v.strip()) for v in paste_values.strip().split('\n') if v.strip()]

                # Create a dataframe with the pasted values
                blocks = range(paste_start_block, paste_start_block + len(values))
                if max(blocks) > 96:
                    st.warning(f"Some values exceed block 96 and will be ignored.")
                    blocks = range(paste_start_block, min(paste_start_block + len(values), 97))
                    values = values[:len(blocks)]

                paste_df = pd.DataFrame({
                    "date": [paste_date] * len(blocks),
                    "block": blocks,
                    "market": values
                })

                # Insert the data into the database
                conn = get_db_connection()
                cursor = conn.cursor()

                for _, row in paste_df.iterrows():
                    # Check if this specific block exists for this date
                    check_block_query = "SELECT id FROM obligation WHERE date = %s AND block = %s"
                    cursor.execute(check_block_query, (row["date"], row["block"]))
                    result = cursor.fetchone()

                    if result:
                        # Update existing record
                        update_query = "UPDATE obligation SET market = %s WHERE date = %s AND block = %s"
                        cursor.execute(update_query, (row["market"], row["date"], row["block"]))
                    else:
                        # Insert new record
                        insert_query = "INSERT INTO obligation (date, block, market) VALUES (%s, %s, %s)"
                        cursor.execute(insert_query, (row["date"], row["block"], row["market"]))

                conn.commit()
                cursor.close()
                conn.close()

                st.success(f"Successfully applied {len(paste_df)} pasted values starting from block {paste_start_block}.")

                # Force a rerun of the app to refresh the data
                st.rerun()
            except Exception as e:
                st.error(f"Error processing pasted values: {str(e)}")

    st.write("---")
    st.write("#### Edit Data Directly")

    edited_df = st.data_editor(
        df,
        num_rows="dynamic",
        use_container_width=True,
        column_config=column_config,
        key="obligation_editor"
    )

    # Handle duplicate date-block combinations by keeping only the last entry
    if not edited_df.empty:
        # Check for duplicates in the edited dataframe
        duplicates = edited_df.duplicated(subset=['date', 'block'], keep=False)
        if duplicates.any():
            # Keep only the last entry for each date-block combination
            edited_df = edited_df.drop_duplicates(subset=['date', 'block'], keep='last')
            st.warning(f"Found duplicate date-block combinations. Keeping only the last entry for each combination.")

        # Ensure blocks are between 1 and 96
        invalid_blocks = edited_df[(edited_df['block'] < 1) | (edited_df['block'] > 96)]
        if not invalid_blocks.empty:
            st.error(f"Time blocks must be between 1 and 96. Please fix these rows:\n{invalid_blocks}")
            return

        # Ensure all 96 blocks exist for each date
        unique_dates = edited_df['date'].unique()
        edited_df = ensure_complete_blocks(edited_df, unique_dates)

        # Sort by date and then by block number
        edited_df = edited_df.sort_values(by=['date', 'block'])

    conn = get_db_connection()
    cursor = conn.cursor()

    # Delete only today's and tomorrow's data
    cursor.execute("DELETE FROM obligation WHERE date IN (%s, %s)", (today, tomorrow))
    conn.commit()

    for _, row in edited_df.iterrows():
        # Convert numpy.float64 to Python float
        market_value = float(row["market"]) if row["market"] is not None else 0.0
        query = "REPLACE INTO obligation (date, block, market) VALUES (%s, %s, %s)"
        cursor.execute(query, (row["date"], row["block"], market_value))

    conn.commit()
    cursor.close()
    conn.close()
    st.success("Obligation data updated successfully.")

def manual_fill_gdam_rtm_ratio():
    st.subheader("GDAM-RTM Ratio Data")

    df = display_table("gdam_rtm_ratio")

    if df.empty:
        df = pd.DataFrame(columns=["block", "gdam", "rtm"])

    # Ensure all 96 blocks exist
    existing_blocks = set(df['block'].tolist()) if not df.empty else set()
    missing_blocks = set(range(1, 97)) - existing_blocks

    if missing_blocks:
        # Add missing blocks with default values
        missing_data = []
        for block in sorted(missing_blocks):
            missing_data.append({"block": block, "gdam": 0.5, "rtm": 0.5})
        missing_df = pd.DataFrame(missing_data)
        df = pd.concat([df, missing_df], ignore_index=True)

    # Sort by block number
    df = df.sort_values(by=['block'])
    df = df.drop(columns=['id'], errors='ignore')

    # Configure the data editor with column configurations
    column_config = {
        "block": st.column_config.NumberColumn(
            "Time Block",
            help="Time block (1-96)",
            min_value=1,
            max_value=96,
            step=1,
            format="%d"
        ),
        "gdam": st.column_config.NumberColumn(
            "GDAM",
            help="GDAM ratio (should sum with RTM to 1.0)",
            min_value=0.0,
            max_value=1.0,
            step=0.01,
            format="%.3f"
        ),
        "rtm": st.column_config.NumberColumn(
            "RTM",
            help="RTM ratio (should sum with GDAM to 1.0)",
            min_value=0.0,
            max_value=1.0,
            step=0.01,
            format="%.3f"
        )
    }

    # Add a helper for clipboard paste
    st.write("#### Quick Paste Helper")
    st.write("If you're having trouble pasting multiple rows directly into the table below, use this helper:")

    col1, col2 = st.columns(2)
    with col1:
        paste_start_block = st.number_input("Starting Block Number", min_value=1, max_value=96, value=1, step=1, key="gdam_rtm_start_block")
    with col2:
        paste_column = st.selectbox("Column to Paste Into", options=["gdam", "rtm"], key="gdam_rtm_column")

    paste_values = st.text_area("Paste Values (one per line)", height=100, key="gdam_rtm_values")

    if st.button("Apply Pasted Values", key="gdam_rtm_apply"):
        if paste_values.strip():
            try:
                # Parse the pasted values
                values = [float(v.strip()) for v in paste_values.strip().split('\n') if v.strip()]

                # Create a list of blocks to update
                blocks = range(paste_start_block, paste_start_block + len(values))
                if max(blocks) > 96:
                    st.warning(f"Some values exceed block 96 and will be ignored.")
                    blocks = range(paste_start_block, min(paste_start_block + len(values), 97))
                    values = values[:len(blocks)]

                # Update the database
                conn = get_db_connection()
                cursor = conn.cursor()

                for i, block in enumerate(blocks):
                    # Use INSERT ... ON DUPLICATE KEY UPDATE to handle both insert and update cases
                    if paste_column == "gdam":
                        # When updating GDAM, calculate RTM as 1 - GDAM for new records, but preserve existing RTM for updates
                        query = """
                        INSERT INTO gdam_rtm_ratio (block, gdam, rtm)
                        VALUES (%s, %s, %s)
                        ON DUPLICATE KEY UPDATE
                        gdam = VALUES(gdam)
                        """
                        # For new records, set RTM to complement GDAM; for existing records, only update GDAM
                        other_value = 1.0 - values[i]
                        cursor.execute(query, (block, values[i], other_value))
                    else:  # paste_column == "rtm"
                        # When updating RTM, calculate GDAM as 1 - RTM for new records, but preserve existing GDAM for updates
                        query = """
                        INSERT INTO gdam_rtm_ratio (block, gdam, rtm)
                        VALUES (%s, %s, %s)
                        ON DUPLICATE KEY UPDATE
                        rtm = VALUES(rtm)
                        """
                        # For new records, set GDAM to complement RTM; for existing records, only update RTM
                        other_value = 1.0 - values[i]
                        cursor.execute(query, (block, other_value, values[i]))

                conn.commit()
                cursor.close()
                conn.close()

                st.success(f"Successfully applied {len(values)} pasted values to {paste_column} starting from block {paste_start_block}.")

                # Force a rerun of the app to refresh the data
                st.rerun()
            except Exception as e:
                st.error(f"Error processing pasted values: {str(e)}")

    st.write("---")
    st.write("#### Edit Data Directly")

    edited_df = st.data_editor(
        df,
        num_rows="dynamic",
        use_container_width=True,
        column_config=column_config,
        key="gdam_rtm_editor"
    )

    # Validate the data
    if not edited_df.empty:
        # Ensure blocks are between 1 and 96
        invalid_blocks = edited_df[(edited_df['block'] < 1) | (edited_df['block'] > 96)]
        if not invalid_blocks.empty:
            st.error(f"Time blocks must be between 1 and 96. Please fix these rows:\n{invalid_blocks}")
            return

        # Check for duplicate blocks
        duplicates = edited_df.duplicated(subset=['block'], keep=False)
        if duplicates.any():
            edited_df = edited_df.drop_duplicates(subset=['block'], keep='last')
            st.warning(f"Found duplicate blocks. Keeping only the last entry for each block.")

        # Validate that GDAM + RTM = 1 for each block (with some tolerance)
        edited_df['sum_check'] = edited_df['gdam'] + edited_df['rtm']
        invalid_sums = edited_df[abs(edited_df['sum_check'] - 1.0) > 0.001]
        if not invalid_sums.empty:
            st.warning("Warning: GDAM + RTM should equal 1.0 for each block. The following blocks have invalid sums:")
            st.dataframe(invalid_sums[['block', 'gdam', 'rtm', 'sum_check']])

        # Remove the sum_check column before saving
        edited_df = edited_df.drop(columns=['sum_check'])

        # Ensure all 96 blocks exist
        existing_blocks = set(edited_df['block'].tolist())
        missing_blocks = set(range(1, 97)) - existing_blocks

        if missing_blocks:
            # Add missing blocks with default values
            missing_data = []
            for block in sorted(missing_blocks):
                missing_data.append({"block": block, "gdam": 0.5, "rtm": 0.5})
            missing_df = pd.DataFrame(missing_data)
            edited_df = pd.concat([edited_df, missing_df], ignore_index=True)

        # Sort by block number
        edited_df = edited_df.sort_values(by=['block'])

    conn = get_db_connection()
    cursor = conn.cursor()

    # Clear existing data
    cursor.execute("DELETE FROM gdam_rtm_ratio")
    conn.commit()

    # Insert updated data
    for _, row in edited_df.iterrows():
        gdam_value = float(row["gdam"]) if row["gdam"] is not None else 0.5
        rtm_value = float(row["rtm"]) if row["rtm"] is not None else 0.5
        query = "INSERT INTO gdam_rtm_ratio (block, gdam, rtm) VALUES (%s, %s, %s)"
        cursor.execute(query, (int(row["block"]), gdam_value, rtm_value))

    conn.commit()
    cursor.close()
    conn.close()
    st.success("GDAM-RTM Ratio data updated successfully.")

def manual_fill_schedule_combined():
    st.subheader("Manual Data Entry: Solar and Wind Schedules")

    # Clean up any duplicate date ranges in both tables
    cleanup_duplicate_dates("schedule_solar", block_column="block", date_column="date")
    cleanup_duplicate_dates("schedule_wind", block_column="block", date_column="date")

    # Get data for both solar and wind
    df_solar = display_table("schedule_solar")
    df_wind = display_table("schedule_wind")
    today = datetime.date.today()
    tomorrow = today + datetime.timedelta(days=1)

    # Process solar data
    if df_solar.empty:
        df_solar = pd.DataFrame(columns=["date", "block", "sch"])
    else:
        df_solar['date'] = pd.to_datetime(df_solar['date']).dt.date
        df_solar = df_solar[df_solar['date'].isin([today, tomorrow])]

    # Process wind data
    if df_wind.empty:
        df_wind = pd.DataFrame(columns=["date", "block", "sch"])
    else:
        df_wind['date'] = pd.to_datetime(df_wind['date']).dt.date
        df_wind = df_wind[df_wind['date'].isin([today, tomorrow])]

    # Ensure all 96 blocks exist for today and tomorrow
    if not df_solar.empty:
        df_solar = ensure_complete_blocks(df_solar, [today, tomorrow])
        # Sort by date and then by block number
        df_solar = df_solar.sort_values(by=['date', 'block'])
    if not df_wind.empty:
        df_wind = ensure_complete_blocks(df_wind, [today, tomorrow])
        # Sort by date and then by block number
        df_wind = df_wind.sort_values(by=['date', 'block'])

    # Add a helper for clipboard paste
    st.write("#### Quick Paste Helper")
    st.write("If you're having trouble pasting multiple rows directly into the tables below, use this helper:")

    col1, col2, col3 = st.columns(3)
    with col1:
        paste_start_block = st.number_input("Starting Block Number", min_value=1, max_value=96, value=1, step=1, key="schedule_start_block")
    with col2:
        paste_date = st.date_input("Date for Pasted Values", value=today, key="schedule_date")
    with col3:
        schedule_type = st.selectbox("Schedule Type", options=["Solar", "Wind"], key="schedule_type")

    paste_values = st.text_area("Paste Schedule Values (one per line)", height=100, key="schedule_values")

    if st.button("Apply Pasted Values", key="schedule_apply"):
        if paste_values.strip():
            try:
                # Parse the pasted values
                values = [float(v.strip()) for v in paste_values.strip().split('\n') if v.strip()]

                # Create a dataframe with the pasted values
                blocks = range(paste_start_block, paste_start_block + len(values))
                if max(blocks) > 96:
                    st.warning(f"Some values exceed block 96 and will be ignored.")
                    blocks = range(paste_start_block, min(paste_start_block + len(values), 97))
                    values = values[:len(blocks)]

                # Insert the data into the database
                conn = get_db_connection()
                cursor = conn.cursor()

                table_name = "schedule_solar" if schedule_type == "Solar" else "schedule_wind"

                # We'll use UPDATE or INSERT for each block individually

                # For each block, either update existing record or insert new one
                for block, value in zip(blocks, values):
                    # Check if this specific block exists for this date
                    check_block_query = f"SELECT id FROM {table_name} WHERE date = %s AND block = %s"
                    cursor.execute(check_block_query, (paste_date, block))
                    result = cursor.fetchone()

                    if result:
                        # Update existing record
                        update_query = f"UPDATE {table_name} SET sch = %s WHERE date = %s AND block = %s"
                        cursor.execute(update_query, (value, paste_date, block))
                    else:
                        # Insert new record
                        insert_query = f"INSERT INTO {table_name} (date, block, sch) VALUES (%s, %s, %s)"
                        cursor.execute(insert_query, (paste_date, block, value))

                conn.commit()

                # No need to execute a sort query here as we'll sort after reloading the data

                cursor.close()
                conn.close()

                st.success(f"Successfully applied {len(values)} pasted values for {schedule_type} schedule starting from block {paste_start_block}.")

                # Force a rerun of the app to refresh the data
                st.rerun()
            except Exception as e:
                st.error(f"Error processing pasted values: {str(e)}")

    st.write("---")

    # Display solar and wind schedules side by side
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("Edit Solar Schedule")
        df_solar = df_solar.drop(columns=['id'], errors='ignore')
        edited_solar = st.data_editor(df_solar, num_rows="dynamic", use_container_width=True, key="solar_editor")

        edited_solar = edited_solar.dropna(subset=['date', 'block'])
        edited_solar['block'] = edited_solar['block'].astype(int)
        edited_solar['sch'] = edited_solar['sch'].where(pd.notnull(edited_solar['sch']), None)

        # Handle duplicate date-block combinations by keeping only the last entry
        if not edited_solar.empty:
            # Check for duplicates in the edited dataframe
            duplicates = edited_solar.duplicated(subset=['date', 'block'], keep=False)
            if duplicates.any():
                # Keep only the last entry for each date-block combination
                edited_solar = edited_solar.drop_duplicates(subset=['date', 'block'], keep='last')
                st.warning(f"Found duplicate date-block combinations in Solar schedule. Keeping only the last entry for each combination.")

            # Ensure blocks are between 1 and 96
            invalid_blocks = edited_solar[(edited_solar['block'] < 1) | (edited_solar['block'] > 96)]
            if not invalid_blocks.empty:
                st.error(f"Time blocks must be between 1 and 96 in Solar schedule. Please fix these rows:\n{invalid_blocks}")
                return

            # Ensure all 96 blocks exist for each date
            unique_dates = edited_solar['date'].unique()
            edited_solar = ensure_complete_blocks(edited_solar, unique_dates)
            # Sort by date and then by block number
            edited_solar = edited_solar.sort_values(by=['date', 'block'])

    with col2:
        st.subheader("Edit Wind Schedule")
        df_wind = df_wind.drop(columns=['id'], errors='ignore')
        edited_wind = st.data_editor(df_wind, num_rows="dynamic", use_container_width=True, key="wind_editor")

        edited_wind = edited_wind.dropna(subset=['date', 'block'])
        edited_wind['block'] = edited_wind['block'].astype(int)
        edited_wind['sch'] = edited_wind['sch'].where(pd.notnull(edited_wind['sch']), None)

        # Handle duplicate date-block combinations by keeping only the last entry
        if not edited_wind.empty:
            # Check for duplicates in the edited dataframe
            duplicates = edited_wind.duplicated(subset=['date', 'block'], keep=False)
            if duplicates.any():
                # Keep only the last entry for each date-block combination
                edited_wind = edited_wind.drop_duplicates(subset=['date', 'block'], keep='last')
                st.warning(f"Found duplicate date-block combinations in Wind schedule. Keeping only the last entry for each combination.")

            # Ensure blocks are between 1 and 96
            invalid_blocks = edited_wind[(edited_wind['block'] < 1) | (edited_wind['block'] > 96)]
            if not invalid_blocks.empty:
                st.error(f"Time blocks must be between 1 and 96 in Wind schedule. Please fix these rows:\n{invalid_blocks}")
                return

            # Ensure all 96 blocks exist for each date
            unique_dates = edited_wind['date'].unique()
            edited_wind = ensure_complete_blocks(edited_wind, unique_dates)
            # Sort by date and then by block number
            edited_wind = edited_wind.sort_values(by=['date', 'block'])

    # Save button for both schedules
    if st.button("Save Both Schedules"):
        conn = get_db_connection()
        cursor = conn.cursor()

        # Update Solar Schedule
        cursor.execute("DELETE FROM schedule_solar WHERE date IN (%s, %s)", (today, tomorrow))
        conn.commit()

        # Convert numpy.float64 to Python float for each row
        solar_data_tuples = []
        for _, row in edited_solar.iterrows():
            sch_value = float(row["sch"]) if row["sch"] is not None else 0.0
            solar_data_tuples.append((row["date"], row["block"], sch_value))

        if solar_data_tuples:
            insert_query = "REPLACE INTO schedule_solar (date, block, sch) VALUES (%s, %s, %s)"
            cursor.executemany(insert_query, solar_data_tuples)
            conn.commit()

        # Update Wind Schedule
        cursor.execute("DELETE FROM schedule_wind WHERE date IN (%s, %s)", (today, tomorrow))
        conn.commit()

        # Convert numpy.float64 to Python float for each row
        wind_data_tuples = []
        for _, row in edited_wind.iterrows():
            sch_value = float(row["sch"]) if row["sch"] is not None else 0.0
            wind_data_tuples.append((row["date"], row["block"], sch_value))

        if wind_data_tuples:
            insert_query = "REPLACE INTO schedule_wind (date, block, sch) VALUES (%s, %s, %s)"
            cursor.executemany(insert_query, wind_data_tuples)
            conn.commit()

        cursor.close()
        conn.close()
        st.success("Solar and Wind schedules updated successfully.")

        # Force a rerun of the app to refresh the data
        st.rerun()

# Keep the original functions for backward compatibility
def manual_fill_schedule_solar():
    st.warning("This page has been replaced by the combined Solar and Wind schedule page. Please use the 'Solar and Wind Schedules' option instead.")
    manual_fill_schedule_combined()

def manual_fill_schedule_wind():
    st.warning("This page has been replaced by the combined Solar and Wind schedule page. Please use the 'Solar and Wind Schedules' option instead.")
    manual_fill_schedule_combined()

# -------------------------------
# Configuration Editors (Contract Value, State, Tariff Difference, Buyer Mapping)
# -------------------------------

def update_contract_value():
    conn = get_db_connection()
    query_buyers = "SELECT DISTINCT buyer_name FROM load_data"
    buyers_df = pd.read_sql(query_buyers, conn)
    existing_df = pd.read_sql("SELECT * FROM contract_value", conn)
    merged = buyers_df.merge(existing_df, on="buyer_name", how="left")
    merged['contract_value'] = merged['contract_value'].fillna(0)

    st.write("Edit Contract Values")
    edited = st.data_editor(merged, num_rows="dynamic", use_container_width=True)

    cursor = conn.cursor()
    for _, row in edited.iterrows():
        buyer = row['buyer_name']
        # Convert numpy.float64 to Python float
        value = float(row['contract_value']) if row['contract_value'] is not None else 0.0
        query = "REPLACE INTO contract_value (buyer_name, contract_value) VALUES (%s, %s)"
        cursor.execute(query, (buyer, value))
    conn.commit()
    cursor.close()
    conn.close()
    st.success("Contract values updated.")

def update_state():
    conn = get_db_connection()
    query_buyers = "SELECT DISTINCT buyer_name FROM load_data"
    buyers_df = pd.read_sql(query_buyers, conn)
    existing_df = pd.read_sql("SELECT * FROM state", conn)
    merged = buyers_df.merge(existing_df, on="buyer_name", how="left")
    merged['state'] = merged['state'].fillna("")

    st.write("Edit States")
    edited = st.data_editor(merged, num_rows="dynamic", use_container_width=True)

    cursor = conn.cursor()
    for _, row in edited.iterrows():
        buyer = row['buyer_name']
        state_val = row['state']
        query = "REPLACE INTO state (buyer_name, state) VALUES (%s, %s)"
        cursor.execute(query, (buyer, state_val))
    conn.commit()
    cursor.close()
    conn.close()
    st.success("States updated.")

def update_tariff_difference():
    conn = get_db_connection()

    # ——— 1) PASTE HELPER UI ———
    st.write("#### Quick Paste Helper")
    st.write("Paste one tariff-difference value per line, starting at the block you choose, for a single state.")

    col1, col2 = st.columns(2)
    with col1:
        paste_start_block = st.number_input(
            "Starting Block Number", min_value=1, max_value=96, value=1, step=1, key="td_start_block"
        )
    with col2:
        # fetch your states list dynamically
        states_df = pd.read_sql("SELECT DISTINCT state FROM state WHERE state <> ''", conn)
        state_options = [""] + sorted(states_df['state'].tolist())
        paste_state = st.selectbox("State", options=state_options, key="td_state")

    paste_values = st.text_area(
        "Paste Tariff-Difference Values (one per line)", height=120, key="td_values"
    )

    if st.button("Apply Tariff Differences", key="td_apply"):
        if not paste_state:
            st.warning("Please select a state.")
        elif not paste_values.strip():
            st.warning("Please paste at least one value.")
        else:
            # parse floats
            try:
                values = [float(v.strip()) for v in paste_values.splitlines() if v.strip()]
            except ValueError:
                st.error("All pasted values must be numeric.")
            else:
                # map to blocks
                blocks = list(range(paste_start_block, paste_start_block + len(values)))
                if max(blocks) > 96:
                    st.warning("Some blocks >96, trimming to 96.")
                    valid = list(range(paste_start_block, 97))
                    values = values[: len(valid)]
                    blocks = valid

                # upsert to tariff_difference
                cur = conn.cursor()
                for blk, td in zip(blocks, values):
                    cur.execute(
                        "SELECT 1 FROM tariff_difference WHERE block=%s AND state=%s",
                        (blk, paste_state),
                    )
                    if cur.fetchone():
                        cur.execute(
                            "UPDATE tariff_difference "
                            "SET tariff_difference=%s WHERE block=%s AND state=%s",
                            (td, blk, paste_state),
                        )
                    else:
                        cur.execute(
                            "INSERT INTO tariff_difference (block, state, tariff_difference) "
                            "VALUES (%s, %s, %s)",
                            (blk, paste_state, td),
                        )
                conn.commit()
                cur.close()
                st.success(f"Applied {len(values)} entries for '{paste_state}'.")

    # ——— 2) FULL GRID EDITOR ———
    # re-fetch distinct states (in case paste helper added new state entries)
    states_df = pd.read_sql("SELECT DISTINCT state FROM state WHERE state <> ''", conn)
    states = sorted(states_df['state'].tolist())

    # build base dataframe of blocks 1–96
    blocks = list(range(1, 97))
    tariff_df = pd.DataFrame({'Block': blocks})

    # pull existing tariff_difference
    existing = pd.read_sql("SELECT * FROM tariff_difference", conn)
    if not existing.empty:
        pivot = (
            existing
            .pivot(index='block', columns='state', values='tariff_difference')
            .reset_index()
            .rename(columns={'block': 'Block'})
        )
        tariff_df = tariff_df.merge(pivot, on='Block', how='left')

    # ensure every state column exists
    for s in states:
        if s not in tariff_df.columns:
            tariff_df[s] = 0.0
    tariff_df = tariff_df[['Block'] + states]

    st.write("#### Edit Tariff Differences (Full Table)")
    edited = st.data_editor(
        tariff_df,
        num_rows="dynamic",
        use_container_width=True,
        key="td_grid"
    )

    # melt back to long form and overwrite the table
    long_df = pd.melt(
        edited,
        id_vars=['Block'],
        var_name='state',
        value_name='tariff_difference'
    )

    cur = conn.cursor()
    cur.execute("DELETE FROM tariff_difference")
    for _, row in long_df.iterrows():
        blk = int(row['Block'])
        st_name = row['state']
        td_val = float(row['tariff_difference']) if pd.notna(row['tariff_difference']) else 0.0
        cur.execute(
            "INSERT INTO tariff_difference (block, state, tariff_difference) VALUES (%s, %s, %s)",
            (blk, st_name, td_val),
        )

    conn.commit()
    cur.close()
    conn.close()

    st.success("Tariff differences updated.")

def update_buyer_mapping():
    st.subheader("Edit Final Revision Integration")
    st.write("Map column names in the final buyer schedule files to actual buyer names")

    conn = get_db_connection()

    # Get the list of buyer names from load_data table
    query_buyers = "SELECT DISTINCT buyer_name FROM load_data"
    buyers_df = pd.read_sql(query_buyers, conn)
    buyer_names = buyers_df['buyer_name'].tolist()

    # Get existing mappings
    mapping_df = pd.read_sql("SELECT * FROM buyer_mapping", conn)

    # Add a form to add a new mapping
    st.write("### Add New Mapping")
    col1, col2, col3 = st.columns(3)
    with col1:
        new_file_column = st.text_input("File Column Name", key="new_file_column", help="The exact column name in the CSV file")
    with col2:
        new_buyer_name = st.selectbox("Actual Buyer Name", options=[""] + buyer_names, key="new_buyer_name", help="The buyer name used in the system")
    with col3:
        new_description = st.text_input("Description", key="new_description", help="Description of the mapping")
    
    if st.button("Add Mapping"):
        if new_file_column and new_buyer_name:
            cursor = conn.cursor()
            try:
                query = """
                INSERT INTO buyer_mapping (file_column_name, actual_buyer_name, description)
                VALUES (%s, %s, %s)
                """
                cursor.execute(query, (new_file_column, new_buyer_name, new_description))
                conn.commit()
                st.success(f"Added new mapping: '{new_file_column}' → '{new_buyer_name}'")
                # Refresh the page to show the new mapping
                st.rerun()
            except Exception as e:
                st.error(f"Error adding mapping: {str(e)}")
            finally:
                cursor.close()
        elif not new_file_column:
            st.warning("Please enter a File Column Name")
        elif not new_buyer_name:
            st.warning("Please select an Actual Buyer Name")

    st.write("---")
    st.write("### Current Buyer Mappings")
    edited = st.data_editor(
        mapping_df,
        num_rows="dynamic",
        use_container_width=True,
        column_config={
            "id": st.column_config.NumberColumn("ID", disabled=True),
            "file_column_name": st.column_config.TextColumn("File Column Name", help="The exact column name in the CSV file"),
            "actual_buyer_name": st.column_config.SelectboxColumn(
                "Actual Buyer Name",
                help="The buyer name used in the system",
                options=buyer_names,
                required=True
            ),
            "description": st.column_config.TextColumn("Description", help="Description of the mapping")
        }
    )

    # Handle updates and inserts
    cursor = conn.cursor()

    # First, get existing IDs
    existing_ids = set(mapping_df['id'].tolist())

    for _, row in edited.iterrows():
        if pd.isna(row['id']) or row['id'] == 0:  # New row
            if not pd.isna(row['file_column_name']) and not pd.isna(row['actual_buyer_name']):
                query = """
                INSERT INTO buyer_mapping (file_column_name, actual_buyer_name, description)
                VALUES (%s, %s, %s)
                """
                description = row['description'] if not pd.isna(row['description']) else ""
                cursor.execute(query, (row['file_column_name'], row['actual_buyer_name'], description))
        else:  # Existing row
            existing_ids.discard(row['id'])  # Remove from set of existing IDs
            if not pd.isna(row['file_column_name']) and not pd.isna(row['actual_buyer_name']):
                query = """
                UPDATE buyer_mapping
                SET file_column_name = %s, actual_buyer_name = %s, description = %s
                WHERE id = %s
                """
                description = row['description'] if not pd.isna(row['description']) else ""
                cursor.execute(query, (row['file_column_name'], row['actual_buyer_name'], description, row['id']))

    # Delete rows that were removed in the UI
    for id_to_delete in existing_ids:
        query = "DELETE FROM buyer_mapping WHERE id = %s"
        cursor.execute(query, (id_to_delete,))

    conn.commit()
    cursor.close()
    conn.close()
    st.success("Buyer mappings updated.")

# -------------------------------
# Bifurcation Logic & CUF% Calculation
# -------------------------------

def compute_cuf(contract_val, current_date):
    days_in_month = calendar.monthrange(current_date.year, current_date.month)[1]
    return contract_val * 96 * days_in_month

def allocate_high_priority(remaining_energy, buyers, req_remaining):
    """
    Allocates energy proportionally to requisitions in one pass while ensuring
    no buyer receives more than its requisition. All allocations are rounded
    to 1 decimal place.
    If any energy remains (because some buyers hit their maximum), the remaining
    energy is redistributed proportionally among those who haven't reached their limit.
    In case a rounding discrepancy of exactly ±0.1 remains, a final adjustment is made.
    For a -0.1 remainder, the adjustment subtracts 0.1 from the buyer with the highest requisition.

    The function ensures that the total allocation never exceeds the available energy.
    """
    # Round the remaining energy to 1 decimal place to ensure consistency
    remaining_energy = round(remaining_energy, 1)

    # Calculate total requested energy.
    total_req = sum(req_remaining[buyer] for buyer in buyers)
    if total_req <= 1e-9:
        return {buyer: 0.0 for buyer in buyers}, remaining_energy

    # First pass: compute each buyer's ideal allocation, capped by their requisition.
    allocations = {}
    for buyer in buyers:
        ideal_allocation = remaining_energy * (req_remaining[buyer] / total_req)
        allocations[buyer] = round(min(ideal_allocation, req_remaining[buyer]), 1)

    allocated_total = round(sum(allocations.values()), 1)
    remainder = round(remaining_energy - allocated_total, 1)

    # Redistribute any remaining energy proportionally among buyers
    # who have not yet reached their requisition limit.
    while remainder > 0:
        capacity_buyers = [buyer for buyer in buyers if allocations[buyer] < req_remaining[buyer]]
        if not capacity_buyers:
            break

        extra_capacity = sum(req_remaining[buyer] - allocations[buyer] for buyer in capacity_buyers)
        if extra_capacity <= 1e-9:
            break

        distributed = 0.0
        for buyer in capacity_buyers:
            cap = req_remaining[buyer] - allocations[buyer]
            additional = remainder * (cap / extra_capacity)
            additional_rounded = round(additional, 1)
            additional_rounded = min(additional_rounded, cap)
            allocations[buyer] = round(allocations[buyer] + additional_rounded, 1)
            distributed += additional_rounded

        distributed = round(distributed, 1)
        new_remainder = round(remainder - distributed, 1)
        if new_remainder == remainder:
            break
        remainder = new_remainder

    # Final adjustment for small rounding errors:
    if remainder == 0.1:
        # Allocate the extra 0.1 to the first buyer with at least 0.1 available capacity.
        for buyer in buyers:
            available = round(req_remaining[buyer] - allocations[buyer], 1)
            if available >= 0.1:
                allocations[buyer] = round(allocations[buyer] + 0.1, 1)
                remainder = 0.0
                break
    elif remainder == -0.1:
        # Subtract 0.1 from the buyer with the highest requisition.
        buyer_to_reduce = max(buyers, key=lambda b: req_remaining[b])
        if allocations[buyer_to_reduce] >= 0.1:
            allocations[buyer_to_reduce] = round(allocations[buyer_to_reduce] - 0.1, 1)
            remainder = 0.0
        else:
            # Fallback: reduce from the first buyer that has at least 0.1 allocated.
            for buyer in buyers:
                if allocations[buyer] >= 0.1:
                    allocations[buyer] = round(allocations[buyer] - 0.1, 1)
                    remainder = 0.0
                    break
    elif remainder < 0:
        # If we somehow allocated more than available (negative remainder),
        # we need to reduce someone's allocation
        while remainder < 0:
            # Find the buyer with the highest allocation
            buyer_to_reduce = max(buyers, key=lambda b: allocations[b])
            if allocations[buyer_to_reduce] >= 0.1:
                allocations[buyer_to_reduce] = round(allocations[buyer_to_reduce] - 0.1, 1)
                remainder = round(remainder + 0.1, 1)
            else:
                # If no buyer has allocation >= 0.1, we can't reduce further
                break

    # Final check to ensure total allocation doesn't exceed remaining_energy
    final_total = round(sum(allocations.values()), 1)
    if final_total > remaining_energy:
        # Find the buyer with the highest allocation to reduce
        buyer_to_reduce = max(buyers, key=lambda b: allocations[b])
        if allocations[buyer_to_reduce] >= 0.1:
            allocations[buyer_to_reduce] = round(allocations[buyer_to_reduce] - 0.1, 1)
            remainder = 0.0

    return allocations, remainder


def allocate_low_priority(remaining_energy, buyers, req_remaining, tariff_diff):
    # Round the remaining energy to 1 decimal place to ensure consistency
    remaining_energy = round(remaining_energy, 1)

    allocations = {buyer: 0.0 for buyer in buyers}
    sorted_buyers = sorted(buyers, key=lambda b: tariff_diff.get(b, 0), reverse=True)

    for buyer in sorted_buyers:
        allocation = min(remaining_energy, req_remaining[buyer])
        allocations[buyer] = round(allocation, 1)  # Round each allocation to 1 decimal place
        req_remaining[buyer] -= allocation
        remaining_energy -= allocation
        remaining_energy = round(remaining_energy, 1)  # Keep rounding to avoid floating point errors

    # Final check to ensure total allocation doesn't exceed original remaining_energy
    total_allocated = round(sum(allocations.values()), 1)
    original_energy = round(sum(allocations.values()) + remaining_energy, 1)

    if total_allocated > original_energy:
        # Find the buyer with the highest allocation to reduce
        if allocations:
            buyer_to_reduce = max(buyers, key=lambda b: allocations.get(b, 0))
            if allocations[buyer_to_reduce] >= 0.1:
                allocations[buyer_to_reduce] = round(allocations[buyer_to_reduce] - 0.1, 1)
                remaining_energy = round(remaining_energy + 0.1, 1)

    return allocations, max(remaining_energy, 0.0)


def update_cuf_pct_table(curr_date, cuf_pct_dict):
    conn = get_db_connection()
    cursor = conn.cursor()
    for buyer, pct in cuf_pct_dict.items():
        # Convert numpy.float64 to Python float
        pct_float = float(pct) if pct is not None else 0.0
        query = "REPLACE INTO cuf_pct (date, buyer_name, cuf_pct) VALUES (%s, %s, %s)"
        cursor.execute(query, (curr_date, buyer, pct_float))
    conn.commit()
    cursor.close()
    conn.close()

def run_bifurcation():
    st.subheader("Running Bifurcation Process")
    create_cuf_pct_table()

    # Check if the final revision file for yesterday exists
    today = datetime.datetime.now().date()
    tomorrow = today + datetime.timedelta(days=1)
    yesterday = today - timedelta(days=1)

    # Connect to FTP and check if the file exists
    ftp = connect_ftp()
    file_name = find_final_buyer_schedule_file(ftp, FTP_PATH_FINAL_BUYER_SCHEDULE, yesterday)

    if not file_name:
        ftp.quit()
        st.error(f"Final Revision Schedule for {yesterday} not found in the folder {FTP_PATH_FINAL_BUYER_SCHEDULE}")
        st.error("Bifurcation will only run when Final Revision Schedule is available for the previous day.")
        st.error("Please process the Final Buyer Schedule first.")
        return

    st.success(f"Found Final Revision Schedule for {yesterday}: {file_name}")

    # Check if CUF% has already been calculated for yesterday
    conn = get_db_connection()
    cuf_check_query = f"SELECT COUNT(*) FROM cuf_pct WHERE date = '{yesterday}'"
    cursor = conn.cursor()
    cursor.execute(cuf_check_query)
    cuf_count = cursor.fetchone()[0]

    # If CUF% hasn't been calculated yet, calculate it now
    if cuf_count == 0:
        st.warning(f"CUF% for {yesterday} has not been calculated yet. Calculating now...")

        # Download the file
        bio = download_ftp_file(ftp, FTP_PATH_FINAL_BUYER_SCHEDULE, file_name)
        ftp.quit()

        if not bio:
            st.error(f"Failed to download {file_name}")
            return

        # Process the file to calculate CUF%
        try:
            # Hard-coded row range (17-115) as specified earlier
            start_row = 17
            end_row = 115
            start_idx = start_row - 11  # Adjust for header at row 11
            end_idx = end_row - 11 + 1  # +1 because end index is exclusive in pandas

            # Read the file with header at row 10 (11th row in the file)
            df = pd.read_csv(bio, header=10)

            # Get the buyer mapping from the database
            buyer_mapping_df = pd.read_sql("SELECT file_column_name, actual_buyer_name FROM buyer_mapping", conn)

            # Create a dictionary for mapping
            buyer_mapping = dict(zip(buyer_mapping_df['file_column_name'], buyer_mapping_df['actual_buyer_name']))

            # Get contract values for CUF calculation
            contract_values_df = pd.read_sql("SELECT buyer_name, contract_value FROM contract_value", conn)
            contract_values = dict(zip(contract_values_df['buyer_name'], contract_values_df['contract_value']))

            # Calculate total energy for each buyer
            buyer_energy = {}

            # Process each mapped buyer column
            for file_col, buyer_name in buyer_mapping.items():
                if file_col in df.columns:
                    # Sum the energy values using the specified row range
                    energy_values = df.iloc[start_idx:end_idx][file_col].astype(float)

                    # Calculate sum
                    total_energy = energy_values.sum()
                    buyer_energy[buyer_name] = total_energy

                    st.write(f"Total energy for {buyer_name}: {total_energy:.2f}")
                else:
                    st.warning(f"Column '{file_col}' not found in the file")

            # Calculate CUF% for each buyer
            cuf_pct = {}
            for buyer, energy in buyer_energy.items():
                if buyer in contract_values and contract_values[buyer] > 0:
                    # Calculate CUF%
                    contract_val = contract_values[buyer]
                    days_in_month = calendar.monthrange(yesterday.year, yesterday.month)[1]
                    denominator = contract_val * 96 * days_in_month
                    daily_cuf_pct = (energy / denominator) * 100 if denominator > 0 else 0
                    cuf_pct[buyer] = daily_cuf_pct
                    st.write(f"Daily CUF% for {buyer}: {daily_cuf_pct:.2f}%")
                else:
                    st.warning(f"No contract value found for {buyer}")

            # Get previous day's cumulative CUF%
            prev_day = yesterday - timedelta(days=1)
            prev_cuf_query = f"SELECT buyer_name, cuf_pct FROM cuf_pct WHERE date = '{prev_day}'"
            prev_cuf_df = pd.read_sql(prev_cuf_query, conn)

            # If no previous day data, try to get the most recent data
            if prev_cuf_df.empty:
                prev_cuf_query = f"""
                    SELECT buyer_name, cuf_pct
                    FROM cuf_pct
                    WHERE date = (
                        SELECT MAX(date)
                        FROM cuf_pct
                        WHERE date < '{yesterday}'
                    )
                """
                prev_cuf_df = pd.read_sql(prev_cuf_query, conn)

            prev_cuf_pct = dict(zip(prev_cuf_df['buyer_name'], prev_cuf_df['cuf_pct'])) if not prev_cuf_df.empty else {}

            # Calculate cumulative CUF%
            cumulative_cuf_pct = {}
            for buyer, daily_pct in cuf_pct.items():
                # For the first day of the month, start fresh
                if yesterday.day == 1:
                    cumulative_cuf_pct[buyer] = daily_pct
                else:
                    # Add previous day's CUF% to accumulate over days
                    prev_cuf = prev_cuf_pct.get(buyer, 0)  # Get last day's CUF or 0
                    cumulative_cuf_pct[buyer] = prev_cuf + daily_pct

            # Update the CUF% table
            for buyer, pct in cumulative_cuf_pct.items():
                query = "REPLACE INTO cuf_pct (date, buyer_name, cuf_pct) VALUES (%s, %s, %s)"
                cursor.execute(query, (yesterday, buyer, float(pct)))

            conn.commit()
            st.success(f"Successfully calculated and stored CUF% for {yesterday}")

        except Exception as e:
            st.error(f"Error calculating CUF% for {yesterday}: {str(e)}")
            cursor.close()
            conn.close()
            return
    else:
        ftp.quit()
        st.success(f"CUF% for {yesterday} already calculated. Using existing values.")

    cursor.close()
    st.write("Fetching source data from MySQL...")
    conn = get_db_connection()

    # --- Load Schedules ---
    solar_df = pd.read_sql("SELECT date, block, sch as sch_solar FROM schedule_solar", conn)
    solar_df['date'] = pd.to_datetime(solar_df['date'])
    wind_df = pd.read_sql("SELECT date, block, sch as sch_wind FROM schedule_wind", conn)
    wind_df['date'] = pd.to_datetime(wind_df['date'])
    schedule_df = pd.merge(solar_df, wind_df, on=["date", "block"], how="outer")
    schedule_df.fillna(0, inplace=True)
    schedule_df["hyb_sch"] = schedule_df["sch_solar"] + schedule_df["sch_wind"]

    # --- Load Obligation Data ---
    obligation_df = pd.read_sql("SELECT date, block, market FROM obligation", conn)
    obligation_df['date'] = pd.to_datetime(obligation_df['date'])
    obligation_df.rename(columns={"market": "GDAM"}, inplace=True)

    # --- Load GDAM-RTM Ratio Data ---
    gdam_rtm_ratio_df = pd.read_sql("SELECT block, gdam, rtm FROM gdam_rtm_ratio", conn)
    # Create a dictionary for quick lookup by block
    gdam_rtm_ratios = dict(zip(gdam_rtm_ratio_df['block'], zip(gdam_rtm_ratio_df['gdam'], gdam_rtm_ratio_df['rtm'])))

    schedule_merged = pd.merge(schedule_df, obligation_df, on=["date", "block"], how="left")
    # Compute Adjusted Schedule
    schedule_merged["Adjusted_Sch"] = np.where(
        schedule_merged["GDAM"] > schedule_merged["hyb_sch"],
        schedule_merged["GDAM"],
        schedule_merged["hyb_sch"]
    )

    # Round schedule and market values to 1 decimal place (except Date and Block)
    cols_to_round = ["sch_solar", "sch_wind", "hyb_sch", "GDAM", "Adjusted_Sch"]
    for col in cols_to_round:
        schedule_merged[col] = schedule_merged[col].round(1)

    schedule_merged.sort_values(["date", "block"], inplace=True)
    schedule_merged.reset_index(drop=True, inplace=True)

    # Fetch contract values.
    contract_value_df = pd.read_sql("SELECT buyer_name, contract_value FROM contract_value", conn)
    contract_values = {row['buyer_name']: row['contract_value'] for _, row in contract_value_df.iterrows()}
    buyer_list = list(contract_values.keys())

    # Fetch Requisition data.
    load_data_df = pd.read_sql("SELECT date, block, buyer_name, load_value FROM load_data", conn)
    load_data_df['date'] = pd.to_datetime(load_data_df['date'])
    if load_data_df.empty:
        st.error("No requisition data found in load_data table.")
        conn.close()
        return
    requisition = load_data_df.pivot_table(index=["date", "block"],
                                           columns="buyer_name",
                                           values="load_value",
                                           aggfunc='first').reset_index()

    # Fetch tariff differences.
    tariff_diff_df = pd.read_sql("SELECT block, state, tariff_difference FROM tariff_difference", conn)
    state_df = pd.read_sql("SELECT buyer_name, state FROM state", conn)
    conn.close()
    buyer_state = {row['buyer_name']: row['state'] for _, row in state_df.iterrows()}

    # -------------------------------
    # Bifurcation Process
    # -------------------------------
    cumulative_allocations = {buyer: {'solar': 0, 'wind': 0} for buyer in buyer_list}
    prev_day_cuf_pct = {buyer: 0 for buyer in buyer_list}
    allocation_results = []

    unique_dates = schedule_merged["date"].dt.date.unique()
    prev_month = None

    # Define ID (Intraday) and DA (Day-Ahead) dates.
    id_date = today
    da_date = tomorrow

    # Use yesterday's CUF% values that we just calculated or verified
    conn = get_db_connection()
    query = f"SELECT buyer_name, cuf_pct FROM cuf_pct WHERE date = '{yesterday}'"
    cuf_data_df = pd.read_sql(query, conn)
    conn.close()

    # Create a dictionary of yesterday's CUF% values
    prev_day_cuf_pct = dict(zip(cuf_data_df['buyer_name'], cuf_data_df['cuf_pct'])) if not cuf_data_df.empty else {}

    # Add debug information for CUF% values
    st.write("### CUF% Values Used for Priority Determination")
    cuf_debug_df = pd.DataFrame(columns=['Buyer', 'CUF%', 'Priority'])
    
    for buyer in buyer_list:
        value = prev_day_cuf_pct.get(buyer, 0)
        priority = "High" if value < 30 else "Low"
        cuf_debug_df = pd.concat([cuf_debug_df, pd.DataFrame({
            'Buyer': [buyer],
            'CUF%': [value],
            'Priority': [priority]
        })], ignore_index=True)
    
    st.dataframe(cuf_debug_df)
    st.write(f"Date of CUF% values: {yesterday}")

    for curr_date in sorted(unique_dates):
        if prev_month is None or curr_date.month != prev_month:
            # Reset cumulative allocations at the beginning of each month
            cumulative_allocations = {buyer: {'solar': 0, 'wind': 0} for buyer in buyer_list}

            # Reset CUF% at the beginning of each month
            if curr_date.day == 1:
                prev_day_cuf_pct = {buyer: 0 for buyer in buyer_list}

            prev_month = curr_date.month

        # Reset cumulative allocations at the beginning of each day to track only today's allocations
        cumulative_allocations = {buyer: {'solar': 0, 'wind': 0} for buyer in buyer_list}

        day_schedule = schedule_merged[schedule_merged["date"].dt.date == curr_date].drop_duplicates(subset=["block"])

        high_priority_buyers = []
        low_priority_buyers = []

        for buyer in buyer_list:
            value = prev_day_cuf_pct.get(buyer, 0)
            if value < 30:
                high_priority_buyers.append(buyer)
            else:
                low_priority_buyers.append(buyer)

        for _, row in day_schedule.iterrows():
            block = row["block"]
            solar_val = row["sch_solar"]
            wind_val = row["sch_wind"]
            total_sch = solar_val + wind_val
            adjusted_sch = row["Adjusted_Sch"]
            remaining_energy = adjusted_sch - row["GDAM"]

            req_row = requisition[
                (pd.to_datetime(requisition["date"]).dt.date == curr_date) &
                (requisition["block"] == block)
            ]
            req_remaining = {buyer: 0 for buyer in buyer_list}
            if not req_row.empty:
                req_row = req_row.iloc[0]
                for buyer in buyer_list:
                    req_remaining[buyer] = req_row.get(buyer, 0)

            allocation_row_total = {buyer: 0 for buyer in buyer_list}

            if high_priority_buyers:
                req_remaining_hp = {buyer: req_remaining[buyer] for buyer in high_priority_buyers}
                hp_alloc, remaining_energy = allocate_high_priority(
                    remaining_energy,
                    high_priority_buyers,
                    req_remaining_hp
                )

                # Update allocations and requisition tracking
                for buyer in high_priority_buyers:
                    allocated = hp_alloc.get(buyer, 0.0)
                    allocation_row_total[buyer] = allocated
                    req_remaining[buyer] -= allocated  # Correctly decrement requisition remaining

            if remaining_energy > 0 and low_priority_buyers:
                tariff_for_buyers = {}
                for buyer in low_priority_buyers:
                    state_val = buyer_state.get(buyer, None)
                    if state_val is not None:
                        tar_row = tariff_diff_df[(tariff_diff_df["block"] == block) &
                                                 (tariff_diff_df["state"] == state_val)]
                        tariff_for_buyers[buyer] = tar_row.iloc[0]["tariff_difference"] if not tar_row.empty else 0
                    else:
                        tariff_for_buyers[buyer] = 0
                req_remaining_lp = {buyer: req_remaining[buyer] for buyer in low_priority_buyers}
                lp_alloc, remaining_energy = allocate_low_priority(
                    remaining_energy,
                    low_priority_buyers,
                    req_remaining_lp,
                    tariff_for_buyers
                )
                for buyer in low_priority_buyers:
                    allocation_row_total[buyer] += lp_alloc.get(buyer, 0)
                    req_remaining[buyer] -= lp_alloc.get(buyer, 0)

            allocation_row = {}

            for buyer in buyer_list:
                allocated = allocation_row_total[buyer]
                if total_sch > 0:
                    alloc_solar = allocated * (solar_val / total_sch)
                    alloc_wind  = allocated * (wind_val / total_sch)
                else:
                    alloc_solar = 0
                    alloc_wind = 0
                allocation_row[f"{buyer}_Solar"] = alloc_solar
                allocation_row[f"{buyer}_Wind"] = alloc_wind
                cumulative_allocations[buyer]['solar'] += alloc_solar
                cumulative_allocations[buyer]['wind'] += alloc_wind

            if total_sch > 0:
                residue_solar = remaining_energy * (solar_val / total_sch)
                residue_wind = remaining_energy * (wind_val / total_sch)
            else:
                residue_solar = 0
                residue_wind = 0

            # Get GDAM-RTM ratios for this block
            gdam_ratio, rtm_ratio = gdam_rtm_ratios.get(block, (0.5, 0.5))  # Default to 50-50 if not found

            # Calculate GDAM and RTM components for Solar and Wind
            gdam_solar = round(residue_solar * gdam_ratio, 1)
            rtm_solar = round(residue_solar * rtm_ratio, 1)
            gdam_wind = round(residue_wind * gdam_ratio, 1)
            rtm_wind = round(residue_wind * rtm_ratio, 1)

            result_entry = {
                "Date": row["date"],
                "Block": block,
                "Solar_Sch": solar_val,
                "Wind_Sch": wind_val,
                "Hyb_Sch": row["hyb_sch"],
                "GDAM": row["GDAM"],
                "Adjusted_Sch": adjusted_sch,
                "GDAM_Solar": gdam_solar,
                "RTM_Solar": rtm_solar,
                "GDAM_Wind": gdam_wind,
                "RTM_Wind": rtm_wind
            }
            for buyer in buyer_list:
                result_entry[f"{buyer}_Solar"] = allocation_row.get(f"{buyer}_Solar", 0)
                result_entry[f"{buyer}_Wind"] = allocation_row.get(f"{buyer}_Wind", 0)
            allocation_results.append(result_entry)

        # No CUF% calculation needed here as we're using yesterday's values from the database

    result_df = pd.DataFrame(allocation_results)
    result_df['Block'] = result_df['Block'].astype(int)
    result_df = result_df.groupby(['Date', 'Block'], as_index=False).first()
    result_df = result_df.sort_values(['Date', 'Block']).round(2)

    result_df["Date_only"] = pd.to_datetime(result_df["Date"]).dt.date

    # Create a "Date + Block" column with Excel serial number + block
    # Step 1: Convert pandas datetime to datetime objects
    date_objects = pd.to_datetime(result_df["Date"])

    # Step 2: Define Excel base date
    excel_base = datetime.datetime(1899, 12, 30)

    # Step 3: Calculate Excel serial numbers
    excel_serials = [(date_obj - excel_base).days for date_obj in date_objects]

    # Step 4: Concatenate with block numbers
    result_df["Date + Block"] = [f"{serial}{block}" for serial, block in zip(excel_serials, result_df["Block"])]

    # Reorder columns to put "Date + Block" before "Date"
    cols = result_df.columns.tolist()
    date_idx = cols.index("Date")
    date_block_idx = cols.index("Date + Block")
    cols.pop(date_block_idx)
    cols.insert(date_idx, "Date + Block")
    result_df = result_df[cols]

    id_results = result_df[result_df["Date_only"] == id_date]
    da_results = result_df[result_df["Date_only"] == da_date]

    st.subheader(f"ID Bifurcation (Intraday) for {id_date}")
    st.dataframe(id_results.drop(columns=["Date_only"]))

    st.subheader(f"DA Bifurcation (Day-Ahead) for {da_date}")
    st.dataframe(da_results.drop(columns=["Date_only"]))

    # Check for energy mismatch and display error if found
    # The total allocated energy should include:
    # 1. The energy allocated to all buyers
    # 2. The remaining unallocated energy
    # 3. The GDAM value (which is allocated separately and not included in allocation_row_total)
    gdam_value = row["GDAM"] if "GDAM" in row else 0
    total_allocated = round(sum(allocation_row_total.values()) + remaining_energy, 1)

    # For the comparison, we need to check if the total allocated energy (including GDAM) matches the adjusted schedule
    if abs((total_allocated + gdam_value) - adjusted_sch) > 0.01 and abs(total_allocated - (adjusted_sch - gdam_value)) > 0.01:
        st.error(f"Energy mismatch in block {block}: {total_allocated} (allocated) + {gdam_value} (GDAM) = {total_allocated + gdam_value} vs {adjusted_sch} (adjusted schedule)")

    # --- Sidebar: Display Previous Day's CUF% (from block 96) --->
    if not cuf_data_df.empty:
        cuf_data_df.set_index("buyer_name", inplace=True)
        st.sidebar.markdown("### Previous Day (Block 96) CUF%")
        st.sidebar.dataframe(cuf_data_df)
    else:
        st.sidebar.write("No CUF% data available from previous dates.")

    csv_data = result_df.to_csv(index=False).encode('utf-8')
    st.download_button(
        label="Download full results as CSV",
        data=csv_data,
        file_name="allocation_results.csv",
        mime="text/csv"
    )

# -------------------------------
# Main Application
# -------------------------------

def main():
    st.title("4th Partner Bifurcation Tool")

    # Add system time to sidebar
    current_time = datetime.datetime.now()
    st.sidebar.write(f"System Time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

    st.sidebar.header("Actions")
    action = st.sidebar.selectbox("Select Action", [
        "Create Tables",
        "Load Data",
        "Manual Data Entry",
        "View Tables",
        "Edit Contract Value",
        "Edit State",
        "Edit Tariff Difference",
        "Edit Final Revision Integration",
        "Process Final Buyer Schedule",
        "Run Bifurcation"
    ])

    # Date calculations
    today = datetime.datetime.now().date()
    tomorrow = today + datetime.timedelta(days=1)
    yesterday = today - datetime.timedelta(days=1)

    if action == "Create Tables":
        create_tables()
        create_config_tables()
        create_gdam_rtm_ratio_table()
        st.success("Data and configuration tables created successfully (if they did not already exist).")

    elif action == "Load Data":
        load_data_to_mysql()

    elif action == "Manual Data Entry":
        table_choice = st.sidebar.selectbox("Select Table to Edit",
                                             ["load_data", "obligation", "solar_and_wind_schedules", "gdam_rtm_ratio"])
        if table_choice == "load_data":
            manual_fill_load_data()
        elif table_choice == "obligation":
            manual_fill_obligation_data()
        elif table_choice == "solar_and_wind_schedules":
            manual_fill_schedule_combined()
        elif table_choice == "gdam_rtm_ratio":
            manual_fill_gdam_rtm_ratio()

    elif action == "View Tables":
        table_options = ["load_data", "obligation", "schedule_solar", "schedule_wind",
                        "contract_value", "state", "tariff_difference", "buyer_mapping", "cuf_pct", "gdam_rtm_ratio"]
        selected_table = st.sidebar.selectbox("Select Table to View", table_options)
        df = display_table(selected_table)

        # Calculate dates
        today = datetime.date.today()
        tomorrow = today + datetime.timedelta(days=1)
        yesterday = today - datetime.timedelta(days=1)

        # Special handling for CUF% table
        if selected_table == "cuf_pct":
            # Always show yesterday's CUF% data
            date_filter = yesterday
            filter_text = "yesterday's"
        elif selected_table == "buyer_mapping":
            # Don't apply date filtering for buyer_mapping
            date_filter = None
            filter_text = "all"
        else:
            # For other tables, show today and tomorrow
            date_filter = [today, tomorrow]
            filter_text = "today's and tomorrow's"

        # Apply date filtering
        date_columns = [col for col in df.columns if col.lower() == "date"]
        if date_columns and date_filter is not None:
            date_col = date_columns[0]
            try:
                if selected_table == "cuf_pct":
                    df = df[pd.to_datetime(df[date_col]).dt.date == date_filter]
                else:
                    df = df[pd.to_datetime(df[date_col]).dt.date.isin(date_filter)]
            except Exception as e:
                st.warning(f"Could not filter dates: {e}")

        st.write(f"Showing {filter_text} data from the '{selected_table}' table:")
        st.dataframe(df)

    elif action == "Edit Contract Value":
        update_contract_value()

    elif action == "Edit State":
        update_state()

    elif action == "Edit Tariff Difference":
        update_tariff_difference()

    elif action == "Edit Final Revision Integration":
        update_buyer_mapping()

    elif action == "Process Final Buyer Schedule":
        st.subheader("Process Final Buyer Schedule")

        # Date selection
        selected_date = st.date_input("Select Date to Process", value=datetime.date.today())

        # Hard-coded values (not displayed to user)
        start_row = 17
        end_row = 115

        if st.button("Process Selected Date"):
            process_final_buyer_schedule(selected_date, start_row, end_row)

        st.write("---")
        st.write("### Process Multiple Dates")
        st.write("Use this option to process a range of dates at once")

        col1, col2 = st.columns(2)
        with col1:
            start_date = st.date_input("Start Date", value=datetime.date.today() - datetime.timedelta(days=7))
        with col2:
            end_date = st.date_input("End Date", value=datetime.date.today())

        if st.button("Process Date Range"):
            if start_date > end_date:
                st.error("Start date must be before or equal to end date")
            else:
                current_date = start_date
                success_count = 0
                fail_count = 0

                while current_date <= end_date:
                    st.write(f"Processing {current_date}...")
                    result = process_final_buyer_schedule(current_date, start_row, end_row)
                    if result:
                        success_count += 1
                    else:
                        fail_count += 1
                    current_date += datetime.timedelta(days=1)

                st.success(f"Processed {success_count} dates successfully. Failed: {fail_count}")

    elif action == "Run Bifurcation":
        run_bifurcation()

if __name__ == "__main__":
    main()
